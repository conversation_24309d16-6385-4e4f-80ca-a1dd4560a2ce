#!/usr/bin/env python3
"""
测试诊断端点
"""

import sys
import os
import json
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def load_env_file():
    """手动加载 .env 文件"""
    env_file = project_root / ".env"
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()

# 在导入其他模块之前加载环境变量
load_env_file()

def test_diagnostic_endpoint():
    """测试诊断端点"""
    try:
        from fastapi.testclient import TestClient
        from ai_gen_hub.api.app import create_app
        
        print("🔍 创建应用并测试诊断端点...")
        
        app = create_app()
        client = TestClient(app)
        
        # 测试诊断端点
        response = client.get("/diagnostic")
        print(f"诊断端点状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 诊断端点访问成功")
            print("\n📊 诊断信息:")
            print(f"   应用环境: {data['app_settings']['environment']}")
            print(f"   调试模式: {data['app_settings']['debug']}")
            print(f"   应用名称: {data['app_settings']['app_name']}")
            print(f"   总路由数: {data['routes']['total']}")
            print(f"   调试路由数: {data['routes']['debug_routes']}")
            print(f"   应用状态中的settings可用: {data['app_state']['settings_available']}")
            print(f"   应该注册调试路由: {data['debug_route_condition']['should_register']}")
            
            print("\n🔍 调试路由列表:")
            for route in data['routes']['debug_routes_list'][:10]:
                print(f"   {route}")
            if len(data['routes']['debug_routes_list']) > 10:
                print(f"   ... 还有 {len(data['routes']['debug_routes_list']) - 10} 个路由")
            
            return True
        else:
            print(f"❌ 诊断端点访问失败: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_lifespan():
    """测试带有lifespan的应用"""
    try:
        print("\n🔍 测试带有lifespan的应用...")
        
        from ai_gen_hub.api.app import AIGenHubApp
        import asyncio
        
        async def test_async():
            # 创建应用实例
            app_instance = AIGenHubApp()
            
            # 初始化应用
            await app_instance.initialize()
            
            # 创建FastAPI应用
            app = app_instance.create_app()
            
            # 手动设置应用状态（模拟lifespan）
            app.state.settings = app_instance.settings
            app.state.health_manager = app_instance.health_manager
            
            # 测试调试端点
            from fastapi.testclient import TestClient
            client = TestClient(app)
            
            response = client.get("/debug/api/system/info")
            print(f"调试端点状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 调试端点访问成功")
                data = response.json()
                print(f"   系统信息: {data.get('system', {}).get('uptime', 'N/A')}")
                return True
            elif response.status_code == 403:
                print("🔒 调试端点访问被拒绝（权限检查）")
                print(response.json())
                return False
            else:
                print(f"❌ 调试端点访问失败: {response.status_code}")
                print(response.text)
                return False
        
        # 运行异步测试
        return asyncio.run(test_async())
        
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 测试诊断功能")
    print("=" * 50)
    
    # 测试诊断端点
    diagnostic_ok = test_diagnostic_endpoint()
    
    # 测试带有lifespan的应用
    lifespan_ok = test_with_lifespan()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    
    if diagnostic_ok:
        print("✅ 诊断端点正常")
    else:
        print("❌ 诊断端点异常")
    
    if lifespan_ok:
        print("✅ 调试端点正常")
    else:
        print("❌ 调试端点异常")
    
    if diagnostic_ok and lifespan_ok:
        print("\n🎉 所有测试通过！")
        return True
    else:
        print("\n⚠️ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
