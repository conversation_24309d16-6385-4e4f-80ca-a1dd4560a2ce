#!/usr/bin/env python3
"""
AI Gen Hub 调试仪表板

集成真实的AI Gen Hub服务API，提供完整的调试和测试功能
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional

import httpx
from fastapi import FastAPI, Request, HTTPException, Depends, Query
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel

# 添加src目录到Python路径以导入AI Gen Hub模块
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

# 尝试导入AI Gen Hub模块
try:
    from ai_gen_hub.config import get_settings
    from ai_gen_hub.api.app import AIGenHubApp
    AI_GEN_HUB_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 无法导入AI Gen Hub模块: {e}")
    print("💡 将使用模拟数据运行调试页面")
    AI_GEN_HUB_AVAILABLE = False

# 尝试导入psutil，如果失败则提供备用实现
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

# 创建FastAPI应用
app = FastAPI(
    title="AI Gen Hub 调试仪表板",
    description="集成真实AI Gen Hub服务的调试页面",
    version="0.1.0"
)

# 设置模板
templates = Jinja2Templates(directory="src/ai_gen_hub/templates")

# AI Gen Hub服务配置
AI_GEN_HUB_BASE_URL = "http://localhost:8001"  # AI Gen Hub服务地址
DEBUG_PORT = 8000  # 调试页面端口

# 配置类
class Settings:
    def __init__(self):
        if AI_GEN_HUB_AVAILABLE:
            try:
                # 使用真实配置
                real_settings = get_settings()
                self.app_name = real_settings.app_name
                self.app_version = real_settings.app_version
                self.environment = real_settings.environment
                self.debug = real_settings.debug
                self.api_host = real_settings.api_host
                self.api_port = real_settings.api_port
            except Exception as e:
                print(f"⚠️ 加载真实配置失败: {e}")
                self._use_mock_settings()
        else:
            self._use_mock_settings()

    def _use_mock_settings(self):
        """使用模拟配置"""
        self.app_name = "AI Gen Hub"
        self.app_version = "0.1.0"
        self.environment = "development"
        self.debug = True
        self.api_host = "0.0.0.0"
        self.api_port = 8001

# 系统信息模型
class SystemInfo(BaseModel):
    cpu_percent: float
    memory_percent: float
    memory_total: int
    memory_available: int
    disk_percent: float
    disk_total: int
    disk_free: int
    uptime: float
    process_count: int
    timestamp: float

# HTTP客户端用于调用AI Gen Hub API
http_client = httpx.AsyncClient(timeout=30.0)

# 初始化应用状态
app.state.settings = Settings()
app.state.start_time = time.time()
app.state.ai_gen_hub_available = AI_GEN_HUB_AVAILABLE

async def call_ai_gen_hub_api(endpoint: str, method: str = "GET", **kwargs) -> Optional[Dict]:
    """调用AI Gen Hub API"""
    if not AI_GEN_HUB_AVAILABLE:
        return None

    try:
        url = f"{AI_GEN_HUB_BASE_URL}{endpoint}"
        response = await http_client.request(method, url, **kwargs)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"⚠️ 调用AI Gen Hub API失败 {endpoint}: {e}")
        return None

def check_debug_access(request: Request) -> bool:
    """检查调试页面访问权限"""
    settings = getattr(request.app.state, "settings", None)
    if not settings:
        raise HTTPException(status_code=500, detail="配置未初始化")
    
    if settings.environment.lower() == "production":
        raise HTTPException(status_code=403, detail="调试页面在生产环境中不可用")
    
    if not settings.debug:
        raise HTTPException(status_code=403, detail="调试页面需要启用调试模式")
    
    return True

def get_system_info() -> SystemInfo:
    """获取系统信息"""
    try:
        if not PSUTIL_AVAILABLE:
            return SystemInfo(
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_total=0,
                memory_available=0,
                disk_percent=0.0,
                disk_total=0,
                disk_free=0,
                uptime=0.0,
                process_count=0,
                timestamp=time.time()
            )
        
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        boot_time = psutil.boot_time()
        uptime = time.time() - boot_time
        process_count = len(psutil.pids())
        
        return SystemInfo(
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_total=memory.total,
            memory_available=memory.available,
            disk_percent=disk.percent,
            disk_total=disk.total,
            disk_free=disk.free,
            uptime=uptime,
            process_count=process_count,
            timestamp=time.time()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")

# 路由定义
@app.get("/", response_class=HTMLResponse)
async def debug_dashboard(request: Request, _: bool = Depends(check_debug_access)):
    """调试仪表板主页"""
    settings = getattr(request.app.state, "settings", None)
    
    context = {
        "request": request,
        "title": "AI Gen Hub 调试仪表板",
        "app_name": settings.app_name if settings else "AI Gen Hub",
        "app_version": settings.app_version if settings else "Unknown",
        "environment": settings.environment if settings else "Unknown",
        "timestamp": datetime.now().isoformat(),
        "page": "dashboard"
    }
    
    return templates.TemplateResponse("debug/dashboard.html", context)

@app.get("/api/system/info")
async def get_system_status(request: Request, _: bool = Depends(check_debug_access)):
    """获取系统状态信息"""
    try:
        system_info = get_system_info()
        settings = getattr(request.app.state, "settings", None)

        app_info = {
            "name": settings.app_name if settings else "Unknown",
            "version": settings.app_version if settings else "Unknown",
            "environment": settings.environment if settings else "Unknown",
            "debug_mode": settings.debug if settings else False,
            "start_time": getattr(request.app.state, "start_time", time.time()),
            "ai_gen_hub_available": getattr(request.app.state, "ai_gen_hub_available", False),
        }

        # 尝试获取真实的健康检查状态
        health_status = await call_ai_gen_hub_api("/health")
        if not health_status:
            # 使用模拟健康检查状态
            health_status = {
                "overall_status": "unknown",
                "checks": [
                    {"name": "ai_gen_hub", "status": "unavailable", "message": "AI Gen Hub服务不可用", "duration": 0.0},
                    {"name": "system", "status": "healthy", "message": "系统资源正常", "duration": 0.02}
                ]
            }

        return {
            "system": system_info.dict(),
            "application": app_info,
            "health": health_status,
            "timestamp": time.time()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/endpoints")
async def get_endpoints(request: Request, _: bool = Depends(check_debug_access)):
    """获取所有API端点信息"""
    try:
        endpoints = []
        
        for route in request.app.routes:
            if hasattr(route, 'methods') and hasattr(route, 'path'):
                for method in route.methods:
                    if method != 'HEAD':
                        endpoint = {
                            "path": route.path,
                            "method": method,
                            "name": getattr(route, 'name', ''),
                            "description": getattr(route, 'description', ''),
                            "tags": getattr(route, 'tags', []),
                            "parameters": {}
                        }
                        endpoints.append(endpoint)
        
        endpoints = sorted(endpoints, key=lambda x: (x['path'], x['method']))
        
        return {
            "endpoints": endpoints,
            "total": len(endpoints),
            "timestamp": time.time()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/system")
async def system_monitor_page(request: Request, _: bool = Depends(check_debug_access)):
    """系统监控页面"""
    context = {
        "request": request,
        "title": "系统状态监控",
        "page": "system"
    }
    return templates.TemplateResponse("debug/system.html", context)

@app.get("/api-test")
async def api_test_page(request: Request, _: bool = Depends(check_debug_access)):
    """API测试页面"""
    context = {
        "request": request,
        "title": "API接口测试",
        "page": "api-test"
    }
    return templates.TemplateResponse("debug/api_test.html", context)

@app.get("/logs")
async def logs_page(request: Request, _: bool = Depends(check_debug_access)):
    """日志查看页面"""
    context = {
        "request": request,
        "title": "日志查看器",
        "page": "logs"
    }
    return templates.TemplateResponse("debug/logs.html", context)

@app.get("/config")
async def config_page(request: Request, _: bool = Depends(check_debug_access)):
    """配置信息页面"""
    context = {
        "request": request,
        "title": "配置信息",
        "page": "config"
    }
    return templates.TemplateResponse("debug/config.html", context)

# AI功能测试端点
@app.post("/api/test/text-generation")
async def test_text_generation(request: dict):
    """测试文本生成功能"""
    try:
        # 调用真实的AI Gen Hub文本生成API
        response = await call_ai_gen_hub_api(
            "/api/v1/text/generate",
            method="POST",
            json=request
        )

        if response:
            return {"success": True, "data": response, "source": "ai_gen_hub"}
        else:
            # 返回模拟响应
            return {
                "success": False,
                "message": "AI Gen Hub服务不可用",
                "mock_response": {
                    "id": "mock-response-123",
                    "choices": [
                        {
                            "message": {
                                "role": "assistant",
                                "content": "这是一个模拟的AI响应，因为真实的AI Gen Hub服务不可用。"
                            },
                            "finish_reason": "stop"
                        }
                    ],
                    "usage": {"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30}
                },
                "source": "mock"
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文本生成测试失败: {str(e)}")

@app.post("/api/test/image-generation")
async def test_image_generation(request: dict):
    """测试图像生成功能"""
    try:
        # 调用真实的AI Gen Hub图像生成API
        response = await call_ai_gen_hub_api(
            "/api/v1/image/generate",
            method="POST",
            json=request
        )

        if response:
            return {"success": True, "data": response, "source": "ai_gen_hub"}
        else:
            # 返回模拟响应
            return {
                "success": False,
                "message": "AI Gen Hub服务不可用",
                "mock_response": {
                    "id": "mock-image-123",
                    "data": [
                        {
                            "url": "https://via.placeholder.com/512x512?text=Mock+Image",
                            "revised_prompt": "模拟图像：" + request.get("prompt", "测试图像")
                        }
                    ]
                },
                "source": "mock"
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"图像生成测试失败: {str(e)}")

@app.get("/api/test/providers")
async def test_providers():
    """测试AI供应商状态"""
    try:
        # 获取供应商信息
        providers_info = await call_ai_gen_hub_api("/api/debug/providers")

        if providers_info:
            return {"success": True, "data": providers_info, "source": "ai_gen_hub"}
        else:
            return {
                "success": False,
                "message": "AI Gen Hub服务不可用",
                "mock_data": {
                    "providers": [
                        {"name": "openai", "status": "unknown", "models": ["gpt-4", "gpt-3.5-turbo"]},
                        {"name": "anthropic", "status": "unknown", "models": ["claude-3-sonnet", "claude-3-haiku"]},
                        {"name": "google_ai", "status": "unknown", "models": ["gemini-pro", "gemini-pro-vision"]}
                    ]
                },
                "source": "mock"
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"供应商测试失败: {str(e)}")

# 添加一些测试端点
@app.get("/test")
async def test_endpoint():
    """测试端点"""
    return {"message": "这是一个测试端点", "timestamp": time.time()}

@app.post("/echo")
async def echo_endpoint(data: dict):
    """回显端点"""
    return {"echo": data, "timestamp": time.time()}

async def cleanup():
    """清理资源"""
    await http_client.aclose()

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    print("🚀 启动AI Gen Hub调试页面...")
    print(f"📍 调试仪表板: http://localhost:{DEBUG_PORT}/")
    print(f"📍 系统监控: http://localhost:{DEBUG_PORT}/system")
    print(f"📍 API测试: http://localhost:{DEBUG_PORT}/api-test")

    if AI_GEN_HUB_AVAILABLE:
        print(f"✅ AI Gen Hub模块可用")
        print(f"🔗 AI Gen Hub服务地址: {AI_GEN_HUB_BASE_URL}")
    else:
        print("⚠️ AI Gen Hub模块不可用，将使用模拟数据")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    await cleanup()
    print("👋 调试页面已关闭")

if __name__ == "__main__":
    import uvicorn

    # 设置环境变量
    os.environ.setdefault('ENVIRONMENT', 'development')
    os.environ.setdefault('DEBUG', 'true')

    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=DEBUG_PORT,
            log_level="info",
            reload=False
        )
    except KeyboardInterrupt:
        print("\n👋 调试页面已停止")
