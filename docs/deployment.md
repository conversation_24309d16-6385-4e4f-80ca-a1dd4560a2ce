# AI Gen Hub 部署指南

## 概述

本文档提供 AI Gen Hub 的完整部署指南，包括 Docker、Kubernetes 和传统部署方式。

## 环境要求

### 最低要求

- **CPU**: 2 核心
- **内存**: 4GB RAM
- **存储**: 10GB 可用空间
- **Python**: 3.9+
- **操作系统**: Linux (推荐 Ubuntu 20.04+)

### 推荐配置

- **CPU**: 4+ 核心
- **内存**: 8GB+ RAM
- **存储**: 50GB+ SSD
- **网络**: 稳定的互联网连接

### 外部依赖

- **Redis** (可选): 用于分布式缓存
- **PostgreSQL** (可选): 用于数据持久化
- **Prometheus** (可选): 用于监控
- **Grafana** (可选): 用于可视化

## Docker 部署

### 1. 使用 Docker Compose (推荐)

创建 `docker-compose.yml` 文件：

```yaml
version: '3.8'

services:
  ai-gen-hub:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - REDIS_URL=redis://redis:6379/0
      - OPENAI_API_KEYS=sk-your-openai-key
      - GOOGLE_AI_API_KEYS=your-google-ai-key
      - ANTHROPIC_API_KEYS=sk-ant-your-anthropic-key
    depends_on:
      - redis
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    restart: unless-stopped

volumes:
  redis_data:
  prometheus_data:
  grafana_data:
```

启动服务：

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f ai-gen-hub

# 停止服务
docker-compose down
```

### 2. 单独使用 Docker

```bash
# 构建镜像
docker build -t ai-gen-hub .

# 运行容器
docker run -d \
  --name ai-gen-hub \
  -p 8000:8000 \
  -e ENVIRONMENT=production \
  -e OPENAI_API_KEYS=sk-your-openai-key \
  -v $(pwd)/logs:/app/logs \
  ai-gen-hub

# 查看日志
docker logs -f ai-gen-hub
```

## Kubernetes 部署

### 1. 创建命名空间

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: ai-gen-hub
```

### 2. 配置 Secret

```yaml
# secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: ai-gen-hub-secrets
  namespace: ai-gen-hub
type: Opaque
stringData:
  openai-api-keys: "sk-your-openai-key-1,sk-your-openai-key-2"
  google-ai-api-keys: "your-google-ai-key"
  anthropic-api-keys: "sk-ant-your-anthropic-key"
  api-key: "your-api-key"
  jwt-secret-key: "your-jwt-secret"
```

### 3. 配置 ConfigMap

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-gen-hub-config
  namespace: ai-gen-hub
data:
  config.yaml: |
    environment: production
    api_host: 0.0.0.0
    api_port: 8000
    debug: false
    
    features:
      enable_text_generation: true
      enable_image_generation: true
      enable_streaming: true
      enable_caching: true
      enable_monitoring: true
      enable_rate_limiting: true
    
    cache:
      enable_memory_cache: true
      enable_redis_cache: true
      memory_cache_size: 1000
      memory_cache_ttl: 3600
      redis_cache_ttl: 7200
    
    redis:
      url: redis://redis-service:6379/0
    
    monitoring:
      log_level: INFO
      health_check_interval: 30
```

### 4. 部署 Redis

```yaml
# redis-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: ai-gen-hub
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command: ["redis-server", "--appendonly", "yes"]
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: ai-gen-hub
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: ai-gen-hub
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
```

### 5. 部署应用

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-gen-hub
  namespace: ai-gen-hub
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-gen-hub
  template:
    metadata:
      labels:
        app: ai-gen-hub
    spec:
      containers:
      - name: ai-gen-hub
        image: ai-gen-hub:latest
        ports:
        - containerPort: 8000
        env:
        - name: CONFIG_FILE
          value: /app/config/config.yaml
        - name: OPENAI_API_KEYS
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: openai-api-keys
        - name: GOOGLE_AI_API_KEYS
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: google-ai-api-keys
        - name: ANTHROPIC_API_KEYS
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: anthropic-api-keys
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: api-key
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: jwt-secret-key
        volumeMounts:
        - name: config
          mountPath: /app/config
        - name: logs
          mountPath: /app/logs
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: config
        configMap:
          name: ai-gen-hub-config
      - name: logs
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: ai-gen-hub-service
  namespace: ai-gen-hub
spec:
  selector:
    app: ai-gen-hub
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-gen-hub-ingress
  namespace: ai-gen-hub
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.yourdomain.com
    secretName: ai-gen-hub-tls
  rules:
  - host: api.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-gen-hub-service
            port:
              number: 80
```

### 6. 部署 HPA (水平自动扩缩)

```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ai-gen-hub-hpa
  namespace: ai-gen-hub
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-gen-hub
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 部署命令

```bash
# 应用所有配置
kubectl apply -f namespace.yaml
kubectl apply -f secrets.yaml
kubectl apply -f configmap.yaml
kubectl apply -f redis-deployment.yaml
kubectl apply -f deployment.yaml
kubectl apply -f hpa.yaml

# 查看部署状态
kubectl get pods -n ai-gen-hub
kubectl get services -n ai-gen-hub
kubectl get ingress -n ai-gen-hub

# 查看日志
kubectl logs -f deployment/ai-gen-hub -n ai-gen-hub
```

## 传统部署

### 1. 系统准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装依赖
sudo apt install -y python3.9 python3.9-venv python3.9-dev \
  build-essential curl git nginx supervisor redis-server

# 创建用户
sudo useradd -m -s /bin/bash ai-gen-hub
sudo usermod -aG sudo ai-gen-hub
```

### 2. 应用部署

```bash
# 切换到应用用户
sudo su - ai-gen-hub

# 克隆代码
git clone https://github.com/your-org/ai-gen-hub.git
cd ai-gen-hub

# 创建虚拟环境
python3.9 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -e .

# 创建配置文件
cp config/config.example.yaml config/config.yaml
# 编辑配置文件...

# 创建日志目录
mkdir -p logs
```

### 3. Supervisor 配置

```ini
# /etc/supervisor/conf.d/ai-gen-hub.conf
[program:ai-gen-hub]
command=/home/<USER>/ai-gen-hub/venv/bin/python -m ai_gen_hub.api.app
directory=/home/<USER>/ai-gen-hub
user=ai-gen-hub
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/home/<USER>/ai-gen-hub/logs/supervisor.log
environment=CONFIG_FILE="/home/<USER>/ai-gen-hub/config/config.yaml"
```

### 4. Nginx 配置

```nginx
# /etc/nginx/sites-available/ai-gen-hub
server {
    listen 80;
    server_name api.yourdomain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        access_log off;
    }
    
    # 静态文件
    location /static {
        alias /home/<USER>/ai-gen-hub/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 5. 启动服务

```bash
# 启用配置
sudo ln -s /etc/nginx/sites-available/ai-gen-hub /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# 启动应用
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start ai-gen-hub

# 查看状态
sudo supervisorctl status ai-gen-hub
```

## 监控配置

### Prometheus 配置

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ai-gen-hub'
    static_configs:
      - targets: ['ai-gen-hub:8000']
    metrics_path: '/metrics/prometheus'
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
```

### Grafana 仪表板

创建 `monitoring/grafana/dashboards/ai-gen-hub.json` 文件，包含以下面板：

- 请求量和响应时间
- 错误率和成功率
- 供应商状态和性能
- 缓存命中率
- 系统资源使用情况

## 安全配置

### 1. 防火墙设置

```bash
# 只开放必要端口
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

### 2. SSL/TLS 配置

```bash
# 使用 Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d api.yourdomain.com
```

### 3. 环境变量安全

```bash
# 使用环境文件
echo "OPENAI_API_KEYS=sk-your-key" | sudo tee /etc/ai-gen-hub/env
sudo chmod 600 /etc/ai-gen-hub/env
sudo chown ai-gen-hub:ai-gen-hub /etc/ai-gen-hub/env
```

## 备份和恢复

### 1. 数据备份

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/ai-gen-hub/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# 备份配置
cp -r /home/<USER>/ai-gen-hub/config $BACKUP_DIR/

# 备份 Redis 数据
redis-cli BGSAVE
cp /var/lib/redis/dump.rdb $BACKUP_DIR/

# 备份日志
cp -r /home/<USER>/ai-gen-hub/logs $BACKUP_DIR/

# 压缩备份
tar -czf $BACKUP_DIR.tar.gz -C /backup/ai-gen-hub $(basename $BACKUP_DIR)
rm -rf $BACKUP_DIR

echo "备份完成: $BACKUP_DIR.tar.gz"
```

### 2. 自动备份

```bash
# 添加到 crontab
0 2 * * * /home/<USER>/backup.sh
```

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查配置文件语法
   - 验证 API 密钥
   - 查看日志文件

2. **高延迟**
   - 检查网络连接
   - 监控资源使用
   - 调整缓存配置

3. **内存不足**
   - 增加服务器内存
   - 调整缓存大小
   - 启用 Redis 缓存

### 日志分析

```bash
# 查看应用日志
tail -f /home/<USER>/ai-gen-hub/logs/app.log

# 查看错误日志
grep ERROR /home/<USER>/ai-gen-hub/logs/app.log

# 查看访问日志
tail -f /var/log/nginx/access.log
```

## 性能优化

### 1. 应用优化

- 启用缓存
- 调整工作进程数
- 优化数据库查询
- 使用连接池

### 2. 系统优化

- 调整内核参数
- 优化网络配置
- 使用 SSD 存储
- 配置 CDN

### 3. 监控告警

设置关键指标的告警：

- CPU 使用率 > 80%
- 内存使用率 > 90%
- 错误率 > 5%
- 响应时间 > 5s
