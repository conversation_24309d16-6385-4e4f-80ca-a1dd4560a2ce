---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-gen-hub
  namespace: ai-gen-hub
  labels:
    app: ai-gen-hub
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ai-gen-hub
  template:
    metadata:
      labels:
        app: ai-gen-hub
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics/prometheus"
    spec:
      containers:
      - name: ai-gen-hub
        image: ai-gen-hub:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        env:
        # 配置文件路径
        - name: CONFIG_FILE
          value: /app/config/config.yaml
        
        # API 密钥
        - name: OPENAI_API_KEYS
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: openai-api-keys
        - name: GOOGLE_AI_API_KEYS
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: google-ai-api-keys
        - name: ANTHROPIC_API_KEYS
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: anthropic-api-keys
        
        # 安全密钥
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: api-key
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: jwt-secret-key
        
        # Pod 信息
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: logs
          mountPath: /app/logs
        
        # 健康检查
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        # 资源限制
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        
        # 安全上下文
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      
      volumes:
      - name: config
        configMap:
          name: ai-gen-hub-config
      - name: logs
        emptyDir: {}
      
      # 调度配置
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - ai-gen-hub
              topologyKey: kubernetes.io/hostname
      
      # 容忍度配置
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300

---
apiVersion: v1
kind: Service
metadata:
  name: ai-gen-hub-service
  namespace: ai-gen-hub
  labels:
    app: ai-gen-hub
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics/prometheus"
spec:
  selector:
    app: ai-gen-hub
  ports:
  - port: 80
    targetPort: 8000
    name: http
  type: ClusterIP
  sessionAffinity: None

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-gen-hub-ingress
  namespace: ai-gen-hub
  annotations:
    # Nginx Ingress 配置
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # 速率限制
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    # 请求大小限制
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    
    # 超时配置
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    
    # WebSocket 支持
    nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "upgrade";
    
    # SSL 证书 (使用 cert-manager)
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    
    # CORS 配置
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-API-Key"
spec:
  tls:
  - hosts:
    - api.yourdomain.com
    secretName: ai-gen-hub-tls
  rules:
  - host: api.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-gen-hub-service
            port:
              number: 80
