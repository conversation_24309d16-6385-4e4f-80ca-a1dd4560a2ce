apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-gen-hub-config
  namespace: ai-gen-hub
data:
  config.yaml: |
    # AI Gen Hub 生产环境配置
    
    # 基础配置
    environment: production
    app_name: "AI Gen Hub"
    app_version: "1.0.0"
    debug: false
    
    # API 服务配置
    api_host: 0.0.0.0
    api_port: 8000
    api_workers: 4
    
    # 功能开关
    features:
      enable_text_generation: true
      enable_image_generation: true
      enable_streaming: true
      enable_caching: true
      enable_monitoring: true
      enable_rate_limiting: true
      enable_load_balancing: true
      enable_circuit_breaker: true
    
    # 供应商配置
    openai:
      enabled: true
      base_url: "https://api.openai.com/v1"
      timeout: 30
      max_retries: 3
      rate_limit_requests: 100
      rate_limit_window: 60
    
    google_ai:
      enabled: true
      base_url: "https://generativelanguage.googleapis.com/v1beta"
      timeout: 30
      max_retries: 3
      rate_limit_requests: 60
      rate_limit_window: 60
    
    anthropic:
      enabled: true
      base_url: "https://api.anthropic.com"
      timeout: 30
      max_retries: 3
      rate_limit_requests: 50
      rate_limit_window: 60
    
    # 缓存配置
    cache:
      enable_memory_cache: true
      enable_redis_cache: true
      memory_cache_size: 1000
      memory_cache_ttl: 3600
      redis_cache_ttl: 7200
      cache_compression: true
    
    # Redis 配置
    redis:
      url: "redis://redis-service:6379/0"
      pool_size: 10
      pool_timeout: 30
    
    # 安全配置
    security:
      cors_origins: ["*"]
      cors_allow_credentials: false
      max_request_size: 10485760  # 10MB
    
    # 性能配置
    performance:
      request_timeout: 60
      max_concurrent_requests: 1000
      rate_limit_requests: 100
      rate_limit_window: 60
    
    # 监控配置
    monitoring:
      log_level: INFO
      log_format: json
      health_check_interval: 30
      metrics_enabled: true
    
    # 数据库配置 (如果需要)
    database:
      url: ""
      pool_size: 10
      pool_timeout: 30
