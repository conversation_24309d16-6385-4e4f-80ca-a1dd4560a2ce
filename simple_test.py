#!/usr/bin/env python3
"""
简单的调试页面测试
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import time
from fastapi import FastAPI
from fastapi.templating import Jinja2Templates

# 模拟配置
class MockSettings:
    def __init__(self):
        self.app_name = "AI Gen Hub"
        self.app_version = "0.1.0"
        self.environment = "development"
        self.debug = True

# 创建应用
app = FastAPI(title="Debug Test")

# 设置模板
templates = Jinja2Templates(directory="src/ai_gen_hub/templates")

# 模拟状态
app.state.settings = MockSettings()
app.state.start_time = time.time()

@app.get("/")
async def root():
    return {"message": "Debug test is running", "status": "ok"}

@app.get("/test")
async def test():
    return {"test": "success", "timestamp": time.time()}

# 尝试导入调试路由
try:
    from src.ai_gen_hub.api.routers.debug import router as debug_router
    app.include_router(debug_router, prefix="/debug", tags=["调试"])
    print("✅ 调试路由加载成功")
except Exception as e:
    print(f"❌ 调试路由加载失败: {e}")

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动测试服务器...")
    print("📍 访问 http://localhost:8000/debug/ 查看调试页面")
    uvicorn.run(app, host="0.0.0.0", port=8000)
