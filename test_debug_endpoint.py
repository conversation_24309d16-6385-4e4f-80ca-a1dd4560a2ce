#!/usr/bin/env python3
"""
测试单个调试端点的详细错误信息
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def load_env_file():
    """手动加载 .env 文件"""
    env_file = project_root / ".env"
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()

# 在导入其他模块之前加载环境变量
load_env_file()

def test_single_endpoint():
    """测试单个端点并显示详细错误"""
    try:
        from fastapi.testclient import TestClient
        from ai_gen_hub.api.app import create_app
        
        app = create_app()
        client = TestClient(app)
        
        # 测试一个简单的端点
        print("测试 /debug/api/system/info 端点...")
        
        try:
            response = client.get("/debug/api/system/info")
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 500:
                print("响应内容:")
                print(response.text)
                
                # 尝试解析JSON错误信息
                try:
                    error_data = response.json()
                    print("错误详情:")
                    print(error_data)
                except:
                    print("无法解析JSON响应")
            else:
                print("响应成功:")
                print(response.json())
                
        except Exception as e:
            print(f"请求异常: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_endpoint()
