"""
Google AI 供应商适配器 - 基于最新 Gemini API 规范

实现 Google AI (Gemini) API 的完整适配，支持最新的 Gemini 2.5 和 2.0 系列模型。

主要功能：
- 文本生成：支持 Gemini 2.5 Pro、2.5 Flash、2.5 Flash-Lite、2.0 Flash 等最新模型
- 多模态输入：支持文本、图像、音频、视频和 PDF 文档
- 系统指令：正确使用 system_instruction 字段而非用户消息
- Thinking 配置：支持 2.5 系列模型的思考过程配置
- 流式输出：实时流式响应支持，包括 SSE 格式处理
- 安全设置：可配置的内容安全过滤
- 结构化输出：支持 JSON 格式响应
- 错误处理：完善的错误处理和重试机制

支持的模型系列：
- Gemini 2.5 系列：最新的高性能模型，支持 Thinking
- Gemini 2.0 系列：下一代功能和超快速度
- Gemini 1.5 系列：向后兼容（将于 2025 年 9 月弃用）

更新日期：2025-08-13
API 版本：v1beta
"""

import json
import time
from typing import Any, AsyncIterator, Dict, List, Optional, Union
from uuid import uuid4

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import (
    ImageGenerationRequest,
    ImageGenerationResponse,
    Message,
    MessageRole,
    ModelType,
    TextGenerationChoice,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    Usage,
)
from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.utils.key_manager import KeyManager


class GoogleAIProvider(BaseProvider):
    """Google AI 供应商适配器

    基于最新 Gemini API 规范的完整实现，支持：

    核心功能：
    - 最新模型支持：Gemini 2.5 Pro/Flash/Flash-Lite、2.0 Flash 等
    - 多模态处理：文本、图像、音频、视频、PDF 文档
    - 系统指令：使用标准 system_instruction 字段
    - Thinking 配置：支持 2.5 系列模型的思考过程
    - 流式响应：实时 SSE 流式输出
    - 安全控制：可配置的内容安全过滤
    - 结构化输出：JSON 格式响应支持

    模型映射：
    - 自动将旧模型名称映射到最新对应模型
    - 支持性能优化的模型选择（fast/lite 别名）
    - 向后兼容 1.0 和 1.5 系列模型名称

    配置选项：
    - thinking_budget：控制 2.5 模型的思考预算
    - safety_settings：自定义安全过滤级别
    - response_format：指定响应格式（text/json）
    """

    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        """初始化 Google AI 适配器

        Args:
            config: Google AI 配置对象，包含 API 设置
            key_manager: 密钥管理器，用于 API 密钥的安全管理
        """
        super().__init__("google_ai", config, key_manager)

        # 设置 Gemini API 基础 URL（使用 v1beta 版本以支持最新功能）
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"

        # 当前支持的模型类型
        self._supported_model_types = [
            ModelType.TEXT_GENERATION,  # 文本生成
            ModelType.IMAGE_GENERATION   # 图像生成（Gemini 2.0 Flash）
        ]
        
        # 支持的模型列表 - 基于最新的 Gemini API 规范
        self._supported_models = [
            # Gemini 2.5 系列 - 最新的高性能模型
            "gemini-2.5-pro",                    # 最先进的推理和编码能力
            "gemini-2.5-flash",                  # 平衡性能和成本的最佳选择
            "gemini-2.5-flash-lite",             # 成本效益最高的轻量级模型

            # Gemini 2.0 系列 - 下一代功能
            "gemini-2.0-flash",                  # 超快速度和原生工具使用
            "gemini-2.0-flash-lite",             # 2.0 系列的轻量级版本

            # Gemini 1.5 系列 - 即将弃用，保持向后兼容
            "gemini-1.5-pro",                   # 复杂推理任务（将于2025年9月弃用）
            "gemini-1.5-flash",                 # 快速多用途模型（将于2025年9月弃用）
            "gemini-1.5-flash-8b",              # 高吞吐量低智能任务（将于2025年9月弃用）

            # 预览版和实验版模型
            "gemini-2.5-flash-preview-05-20",   # 2.5 Flash 预览版
            "gemini-2.0-flash-exp",             # 2.0 Flash 实验版

            # 向后兼容的旧模型名称（映射到新模型）
            "gemini-pro",                        # 映射到 gemini-2.5-pro
            "gemini-pro-vision",                 # 映射到 gemini-2.5-pro
            "gemini-1.0-pro",                   # 映射到 gemini-2.5-pro
            "gemini-1.0-pro-001",               # 映射到 gemini-2.5-pro
            "gemini-1.0-pro-latest",            # 映射到 gemini-2.5-pro
            "gemini-1.0-pro-vision-latest",     # 映射到 gemini-2.5-pro
        ]

        # 模型映射 - 将旧模型名称映射到最新的对应模型
        self._model_mapping = {
            # 通用别名映射到最新的推荐模型
            "gemini-latest": "gemini-2.5-flash",        # 默认推荐最平衡的模型
            "gemini": "gemini-2.5-flash",               # 简单别名

            # 旧模型名称映射到新模型（保持向后兼容）
            "gemini-pro": "gemini-2.5-pro",            # 旧的 pro 模型映射到新的 pro
            "gemini-pro-vision": "gemini-2.5-pro",     # 视觉模型映射到支持多模态的 pro
            "gemini-1.0-pro": "gemini-2.5-pro",        # 1.0 pro 映射到 2.5 pro
            "gemini-1.0-pro-001": "gemini-2.5-pro",    # 具体版本映射
            "gemini-1.0-pro-latest": "gemini-2.5-pro", # latest 版本映射
            "gemini-1.0-pro-vision-latest": "gemini-2.5-pro", # 视觉版本映射

            # 性能优化映射
            "gemini-fast": "gemini-2.5-flash",         # 快速模型别名
            "gemini-lite": "gemini-2.5-flash-lite",    # 轻量级模型别名
        }
    
    async def _perform_health_check(self, api_key: str) -> bool:
        """执行 API 健康检查

        通过请求模型列表来验证 API 密钥的有效性和服务可用性。
        这是一个轻量级的检查，不会消耗生成配额。

        Args:
            api_key: 要验证的 Google AI API 密钥

        Returns:
            bool: 如果 API 可用且密钥有效则返回 True，否则返回 False
        """
        try:
            # 发送模型列表请求来验证 API 密钥和服务状态
            url = f"{self.base_url}/models?key={api_key}"
            response = await self._make_request("GET", url)
            return response.status_code == 200
        except Exception as e:
            # 记录健康检查失败的详细信息
            self.logger.warning(f"Google AI API 健康检查失败: {e}")
            return False
    
    async def _generate_text_impl(
        self,
        request: TextGenerationRequest,
        api_key: str
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """实现文本生成的核心逻辑

        根据请求类型（流式或非流式）调用相应的 Gemini API 端点。
        自动处理模型名称映射、请求构建和响应解析。

        Args:
            request: 文本生成请求对象，包含模型、消息、参数等
            api_key: 有效的 Google AI API 密钥

        Returns:
            Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
                非流式请求返回完整响应，流式请求返回响应块的异步迭代器
        """
        # 构建符合 Gemini API 规范的请求数据
        request_data = self._build_text_request(request)

        # 映射模型名称（处理别名和向后兼容）
        model_name = self.map_model_name(request.model)

        if request.stream:
            # 流式生成：使用 streamGenerateContent 端点
            url = f"{self.base_url}/models/{model_name}:streamGenerateContent?key={api_key}"
            return self._handle_stream_response(request, url, request_data)
        else:
            # 非流式生成：使用 generateContent 端点
            url = f"{self.base_url}/models/{model_name}:generateContent?key={api_key}"
            response = await self._make_request("POST", url, json_data=request_data)
            response_data = response.json()
            return self._parse_text_response(response_data, request)
    
    def _build_text_request(self, request: TextGenerationRequest) -> Dict[str, Any]:
        """构建文本生成请求数据

        根据最新的 Gemini API 规范构建请求，支持：
        - 系统指令（system_instruction）
        - Thinking 配置（thinkingConfig）
        - 多模态内容处理
        - 安全设置和生成配置
        """
        # 分离系统消息和对话消息
        system_messages = []
        conversation_messages = []

        for msg in request.messages:
            if msg.role == MessageRole.SYSTEM:
                system_messages.append(msg)
            else:
                conversation_messages.append(msg)

        # 构建系统指令（如果有系统消息）
        system_instruction = None
        if system_messages:
            # 合并所有系统消息为一个系统指令
            system_content = "\n".join([msg.content for msg in system_messages])
            system_instruction = {
                "parts": [{"text": system_content}]
            }

        # 转换对话消息格式为 Google AI 格式
        contents = []
        current_role = None
        current_parts = []

        for msg in conversation_messages:
            # 映射角色（不再处理 SYSTEM，因为已经单独处理）
            if msg.role == MessageRole.USER:
                role = "user"
            elif msg.role == MessageRole.ASSISTANT:
                role = "model"
            else:
                # 跳过不支持的角色
                continue

            if role != current_role:
                # 保存之前的内容
                if current_role and current_parts:
                    contents.append({
                        "role": current_role,
                        "parts": current_parts
                    })

                # 开始新的角色
                current_role = role
                current_parts = []

            # 添加消息内容
            current_parts.append({"text": msg.content})

        # 添加最后的内容
        if current_role and current_parts:
            contents.append({
                "role": current_role,
                "parts": current_parts
            })

        # 构建基础请求数据
        request_data = {"contents": contents}

        # 添加系统指令（如果有）
        if system_instruction:
            request_data["system_instruction"] = system_instruction
        
        # 添加生成配置
        generation_config = {}

        # 基础生成参数
        if request.max_tokens is not None:
            generation_config["maxOutputTokens"] = request.max_tokens
        if request.temperature is not None:
            generation_config["temperature"] = request.temperature
        if request.top_p is not None:
            generation_config["topP"] = request.top_p
        if request.top_k is not None:
            generation_config["topK"] = request.top_k
        if request.stop is not None:
            if isinstance(request.stop, str):
                generation_config["stopSequences"] = [request.stop]
            else:
                generation_config["stopSequences"] = request.stop

        # 检查是否为支持 Thinking 的模型（2.5 系列）
        model_name = self.map_model_name(request.model)
        is_thinking_model = any(model_name.startswith(prefix) for prefix in [
            "gemini-2.5-pro", "gemini-2.5-flash"
        ])

        # 添加 Thinking 配置（仅适用于 2.5 系列模型）
        if is_thinking_model:
            # 检查是否有自定义的 thinking 配置
            thinking_config = getattr(request, 'thinking_config', None)
            if thinking_config is not None:
                # 如果用户明确设置了 thinking 配置
                if isinstance(thinking_config, dict):
                    generation_config["thinkingConfig"] = thinking_config
                elif hasattr(thinking_config, 'thinking_budget'):
                    # 支持对象形式的配置
                    generation_config["thinkingConfig"] = {
                        "thinkingBudget": thinking_config.thinking_budget
                    }
            else:
                # 默认情况下，2.5 模型启用 thinking
                # 用户可以通过设置 thinking_budget=0 来禁用
                thinking_budget = getattr(request, 'thinking_budget', None)
                if thinking_budget is not None:
                    generation_config["thinkingConfig"] = {
                        "thinkingBudget": thinking_budget
                    }
                # 如果没有明确设置，使用模型默认值（启用 thinking）

        # 添加结构化输出配置
        self._add_structured_output_config(request, generation_config, request_data)

        if generation_config:
            request_data["generationConfig"] = generation_config
        
        # 安全设置配置
        # 允许用户自定义安全设置，否则使用默认的中等安全级别
        safety_settings = getattr(request, 'safety_settings', None)
        if safety_settings is None:
            # 默认安全设置 - 阻止中等及以上级别的有害内容
            safety_settings = [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_HATE_SPEECH",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                }
            ]

        # 只有在有安全设置时才添加到请求中
        if safety_settings:
            request_data["safetySettings"] = safety_settings

        # 添加工具配置
        self._add_tools_config(request, request_data)
        
        return request_data

    def _add_structured_output_config(
        self,
        request: TextGenerationRequest,
        generation_config: Dict[str, Any],
        request_data: Dict[str, Any]
    ) -> None:
        """添加结构化输出配置

        支持 Gemini API 的结构化输出功能，包括：
        - JSON Schema 约束
        - 响应格式配置
        - 枚举值约束

        Args:
            request: 文本生成请求
            generation_config: 生成配置字典
            request_data: 请求数据字典
        """
        # 处理响应格式配置
        response_format = getattr(request, 'response_format', None)
        if response_format:
            if response_format.get('type') == 'json_object':
                generation_config["responseMimeType"] = "application/json"
            elif response_format.get('type') == 'text':
                generation_config["responseMimeType"] = "text/plain"
            elif response_format.get('type') == 'enum':
                generation_config["responseMimeType"] = "text/x.enum"

        # 处理 JSON Schema 约束
        response_schema = getattr(request, 'response_schema', None)
        if response_schema:
            # 验证 schema 格式
            if self._validate_json_schema(response_schema):
                generation_config["responseSchema"] = response_schema
                # 如果有 schema 但没有明确设置 MIME 类型，默认为 JSON
                if "responseMimeType" not in generation_config:
                    generation_config["responseMimeType"] = "application/json"
            else:
                self.logger.warning("提供的 JSON Schema 格式无效，将忽略结构化输出约束")

    def _validate_json_schema(self, schema: Dict[str, Any]) -> bool:
        """验证 JSON Schema 格式

        检查提供的 schema 是否符合 Gemini API 支持的格式。

        Args:
            schema: 要验证的 JSON Schema

        Returns:
            bool: 是否为有效的 schema
        """
        if not isinstance(schema, dict):
            return False

        # 检查必需的字段
        if "type" not in schema:
            return False

        # 验证支持的类型
        supported_types = ["object", "array", "string", "integer", "number", "boolean"]
        if schema["type"] not in supported_types:
            return False

        # 对于 object 类型，检查 properties
        if schema["type"] == "object":
            if "properties" in schema:
                if not isinstance(schema["properties"], dict):
                    return False
                # 递归验证属性
                for prop_schema in schema["properties"].values():
                    if not self._validate_json_schema(prop_schema):
                        return False

        # 对于 array 类型，检查 items
        elif schema["type"] == "array":
            if "items" in schema:
                if not self._validate_json_schema(schema["items"]):
                    return False

        # 对于 string 类型，检查 enum
        elif schema["type"] == "string":
            if "enum" in schema:
                if not isinstance(schema["enum"], list):
                    return False
                # 确保所有枚举值都是字符串
                if not all(isinstance(item, str) for item in schema["enum"]):
                    return False

        return True

    async def _generate_image_impl(
        self,
        request: ImageGenerationRequest,
        api_key: str
    ) -> ImageGenerationResponse:
        """实现图像生成的核心逻辑

        使用 Gemini 2.0 Flash 的图像生成能力，支持：
        - 文本到图像生成
        - 图像编辑（文本+图像到图像）
        - 多模态响应（文本+图像）

        Args:
            request: 图像生成请求对象
            api_key: 有效的 Google AI API 密钥

        Returns:
            ImageGenerationResponse: 图像生成响应
        """
        # 构建图像生成请求数据
        request_data = self._build_image_request(request)

        # 使用支持图像生成的模型
        model_name = request.model or "gemini-2.0-flash-preview-image-generation"

        # 构建请求 URL
        url = f"{self.base_url}/models/{model_name}:generateContent?key={api_key}"

        # 发送请求
        response = await self._make_request("POST", url, json_data=request_data)
        response_data = response.json()

        # 解析响应
        return self._parse_image_response(response_data, request)

    def _build_image_request(self, request: ImageGenerationRequest) -> Dict[str, Any]:
        """构建图像生成请求数据

        根据 Gemini API 规范构建图像生成请求，支持：
        - 纯文本提示生成图像
        - 文本+图像输入进行图像编辑
        - 多模态响应配置

        Args:
            request: 图像生成请求

        Returns:
            Dict[str, Any]: 符合 Gemini API 格式的请求数据
        """
        # 构建内容部分
        contents = []
        parts = []

        # 添加文本提示
        if request.prompt:
            parts.append({"text": request.prompt})

        # 添加输入图像（用于图像编辑）
        if request.input_images:
            for image_data in request.input_images:
                # 假设输入是 base64 编码的图像数据
                parts.append({
                    "inline_data": {
                        "mime_type": "image/png",  # 可以根据实际情况调整
                        "data": image_data
                    }
                })

        # 添加编辑指令
        if request.edit_instruction:
            parts.append({"text": request.edit_instruction})

        contents.append({
            "role": "user",
            "parts": parts
        })

        # 构建生成配置
        generation_config = {
            "responseModalities": request.response_modalities or ["TEXT", "IMAGE"]
        }

        # 添加质量和尺寸配置（如果支持）
        if hasattr(request, 'quality') and request.quality:
            generation_config["quality"] = request.quality

        # 构建请求数据
        request_data = {
            "contents": contents,
            "generationConfig": generation_config
        }

        return request_data

    def _parse_image_response(
        self,
        response_data: Dict[str, Any],
        request: ImageGenerationRequest
    ) -> ImageGenerationResponse:
        """解析图像生成响应

        解析 Gemini API 的图像生成响应，提取图像数据和元数据。

        Args:
            response_data: API 响应数据
            request: 原始请求

        Returns:
            ImageGenerationResponse: 标准化的图像生成响应
        """
        import time
        from uuid import uuid4
        import base64

        image_data_list = []

        # 解析候选响应
        candidates = response_data.get("candidates", [])
        for candidate in candidates:
            content = candidate.get("content", {})
            parts = content.get("parts", [])

            for part in parts:
                # 处理内联图像数据
                if "inline_data" in part:
                    inline_data = part["inline_data"]
                    image_data = inline_data.get("data", "")

                    # 创建图像数据对象
                    image_obj = ImageData(
                        b64_json=image_data,
                        revised_prompt=request.prompt  # 可以从响应中获取修订后的提示
                    )
                    image_data_list.append(image_obj)

        # 如果没有找到图像数据，创建一个空的响应
        if not image_data_list:
            self.logger.warning("图像生成响应中未找到图像数据")
            image_data_list.append(ImageData())

        return ImageGenerationResponse(
            created=int(time.time()),
            data=image_data_list,
            provider=self.name,
            request_id=str(uuid4()),
            processing_time=0.0  # 可以从实际请求时间计算
        )

    def _add_tools_config(
        self,
        request: TextGenerationRequest,
        request_data: Dict[str, Any]
    ) -> None:
        """添加工具配置

        支持 Gemini API 的工具集成功能，包括：
        - Google Search 工具
        - 代码执行工具
        - 自定义函数调用

        Args:
            request: 文本生成请求
            request_data: 请求数据字典
        """
        tools = []

        # 处理传统的 functions 参数（转换为 tools 格式）
        if request.functions:
            function_declarations = []
            for func in request.functions:
                function_declarations.append(self._convert_function_to_declaration(func))

            if function_declarations:
                tools.append({
                    "function_declarations": function_declarations
                })

        # 处理新的 tools 参数
        if request.tools:
            for tool in request.tools:
                converted_tool = self._convert_tool_format(tool)
                if converted_tool:
                    tools.append(converted_tool)

        # 添加内置工具支持
        builtin_tools = self._get_builtin_tools(request)
        tools.extend(builtin_tools)

        # 如果有工具，添加到请求中
        if tools:
            request_data["tools"] = tools

            # 添加工具配置
            tool_config = self._build_tool_config(request)
            if tool_config:
                request_data["tool_config"] = tool_config

    def _convert_function_to_declaration(self, func: Dict[str, Any]) -> Dict[str, Any]:
        """将函数定义转换为 Gemini API 的函数声明格式

        Args:
            func: 函数定义

        Returns:
            Dict[str, Any]: Gemini API 格式的函数声明
        """
        declaration = {
            "name": func.get("name", ""),
            "description": func.get("description", ""),
        }

        # 转换参数定义
        if "parameters" in func:
            declaration["parameters"] = func["parameters"]

        return declaration

    def _convert_tool_format(self, tool: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """转换工具格式为 Gemini API 支持的格式

        Args:
            tool: 工具定义

        Returns:
            Optional[Dict[str, Any]]: 转换后的工具定义，如果不支持则返回 None
        """
        # 处理函数调用工具
        if "function" in tool:
            return {
                "function_declarations": [self._convert_function_to_declaration(tool["function"])]
            }

        # 处理 Google Search 工具
        elif "google_search" in tool or tool.get("type") == "google_search":
            return {"google_search": {}}

        # 处理代码执行工具
        elif "code_execution" in tool or tool.get("type") == "code_execution":
            return {"code_execution": {}}

        # 处理其他工具类型
        else:
            self.logger.warning(f"不支持的工具类型: {tool}")
            return None

    def _get_builtin_tools(self, request: TextGenerationRequest) -> List[Dict[str, Any]]:
        """获取内置工具配置

        根据请求参数自动启用相关的内置工具。

        Args:
            request: 文本生成请求

        Returns:
            List[Dict[str, Any]]: 内置工具列表
        """
        builtin_tools = []

        # 检查是否需要 Google Search
        if self._should_enable_google_search(request):
            builtin_tools.append({"google_search": {}})

        # 检查是否需要代码执行
        if self._should_enable_code_execution(request):
            builtin_tools.append({"code_execution": {}})

        return builtin_tools

    def _should_enable_google_search(self, request: TextGenerationRequest) -> bool:
        """判断是否应该启用 Google Search 工具

        Args:
            request: 文本生成请求

        Returns:
            bool: 是否启用 Google Search
        """
        # 检查提供商特定参数
        provider_params = getattr(request, 'provider_params', {})
        if provider_params.get('enable_google_search', False):
            return True

        # 检查消息内容是否包含搜索相关的关键词
        search_keywords = ['搜索', 'search', '查找', '最新', '当前', '实时']
        for message in request.messages:
            content = message.content.lower()
            if any(keyword in content for keyword in search_keywords):
                return True

        return False

    def _should_enable_code_execution(self, request: TextGenerationRequest) -> bool:
        """判断是否应该启用代码执行工具

        Args:
            request: 文本生成请求

        Returns:
            bool: 是否启用代码执行
        """
        # 检查提供商特定参数
        provider_params = getattr(request, 'provider_params', {})
        if provider_params.get('enable_code_execution', False):
            return True

        # 检查消息内容是否包含编程相关的关键词
        code_keywords = ['代码', 'code', '编程', '计算', '算法', 'python', 'javascript']
        for message in request.messages:
            content = message.content.lower()
            if any(keyword in content for keyword in code_keywords):
                return True

        return False

    def _build_tool_config(self, request: TextGenerationRequest) -> Optional[Dict[str, Any]]:
        """构建工具配置

        Args:
            request: 文本生成请求

        Returns:
            Optional[Dict[str, Any]]: 工具配置，如果没有特殊配置则返回 None
        """
        tool_config = {}

        # 处理函数调用配置
        if request.function_call or request.tool_choice:
            function_calling_config = {}

            # 处理函数调用模式
            if request.function_call:
                if request.function_call == "auto":
                    function_calling_config["mode"] = "AUTO"
                elif request.function_call == "none":
                    function_calling_config["mode"] = "NONE"
                elif isinstance(request.function_call, dict):
                    function_calling_config["mode"] = "ANY"
                    if "name" in request.function_call:
                        function_calling_config["allowed_function_names"] = [
                            request.function_call["name"]
                        ]

            # 处理工具选择配置
            elif request.tool_choice:
                if request.tool_choice == "auto":
                    function_calling_config["mode"] = "AUTO"
                elif request.tool_choice == "none":
                    function_calling_config["mode"] = "NONE"
                elif isinstance(request.tool_choice, dict):
                    function_calling_config["mode"] = "ANY"
                    if "function" in request.tool_choice:
                        function_calling_config["allowed_function_names"] = [
                            request.tool_choice["function"]["name"]
                        ]

            if function_calling_config:
                tool_config["function_calling_config"] = function_calling_config

        return tool_config if tool_config else None
    
    def _parse_text_response(
        self,
        response_data: Dict[str, Any],
        request: TextGenerationRequest
    ) -> TextGenerationResponse:
        """解析文本生成响应

        根据最新的 Gemini API 响应格式解析结果，支持：
        - 多候选响应处理
        - 增强的结束原因映射
        - 详细的使用量统计
        - Thinking 相关的元数据
        """
        choices = []

        # 解析候选响应
        candidates = response_data.get("candidates", [])
        for i, candidate in enumerate(candidates):
            content = candidate.get("content", {})
            parts = content.get("parts", [])

            # 合并所有文本部分
            text_content = ""
            for part in parts:
                if "text" in part:
                    text_content += part["text"]

            message = Message(
                role=MessageRole.ASSISTANT,
                content=text_content
            )

            # 获取结束原因并映射到标准格式
            finish_reason = candidate.get("finishReason", "stop")
            finish_reason_mapping = {
                "STOP": "stop",
                "MAX_TOKENS": "length",
                "SAFETY": "content_filter",
                "RECITATION": "content_filter",  # 新增：引用检测
                "OTHER": "stop",                 # 新增：其他原因
                "BLOCKLIST": "content_filter",   # 新增：阻止列表
                "PROHIBITED_CONTENT": "content_filter",  # 新增：禁止内容
                "SPII": "content_filter",        # 新增：敏感个人信息
                "MALFORMED_FUNCTION_CALL": "stop"  # 新增：函数调用格式错误
            }
            mapped_finish_reason = finish_reason_mapping.get(finish_reason, "stop")

            # 获取安全评级信息（如果有）
            safety_ratings = candidate.get("safetyRatings", [])

            choice = TextGenerationChoice(
                index=i,
                message=message,
                finish_reason=mapped_finish_reason
            )

            # 如果有安全评级，添加到选择的元数据中
            if safety_ratings:
                choice.safety_ratings = safety_ratings

            choices.append(choice)

        # 解析使用量统计 - 支持更详细的 token 计数
        usage_metadata = response_data.get("usageMetadata", {})

        # 获取基础 token 计数
        prompt_tokens = usage_metadata.get("promptTokenCount", 0)
        completion_tokens = usage_metadata.get("candidatesTokenCount", 0)
        total_tokens = usage_metadata.get("totalTokenCount", 0)

        # 如果没有总计数，则计算
        if total_tokens == 0 and (prompt_tokens > 0 or completion_tokens > 0):
            total_tokens = prompt_tokens + completion_tokens

        usage = Usage(
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens
        )

        # 添加 Thinking 相关的使用量统计（如果有）
        if "thinkingTokenCount" in usage_metadata:
            usage.thinking_tokens = usage_metadata["thinkingTokenCount"]

        return TextGenerationResponse(
            id=str(uuid4()),
            object="chat.completion",
            created=int(time.time()),
            model=request.model,
            choices=choices,
            usage=usage,
            provider=self.name,
            request_id=str(uuid4()),
            processing_time=0.0
        )
    
    async def _handle_stream_response(
        self,
        request: TextGenerationRequest,
        url: str,
        request_data: Dict[str, Any]
    ) -> AsyncIterator[TextGenerationStreamChunk]:
        """处理流式响应

        处理 Gemini API 的服务器发送事件（SSE）流式响应，支持：
        - 增量文本内容
        - 实时的结束原因检测
        - Thinking 过程的流式输出（如果启用）
        - 错误处理和重试
        """
        async for chunk in self._make_request(
            "POST",
            url,
            json_data=request_data,
            stream=True
        ):
            # 解析流式数据 - 支持 SSE 格式
            chunk_str = chunk.decode('utf-8')
            for line in chunk_str.split('\n'):
                line = line.strip()

                # 处理 SSE 数据行
                if line.startswith('data: '):
                    data_str = line[6:]

                    # 跳过空数据或结束标记
                    if not data_str or data_str == '[DONE]':
                        continue

                    try:
                        data = json.loads(data_str)

                        # 解析候选响应
                        candidates = data.get("candidates", [])
                        choices = []

                        for i, candidate in enumerate(candidates):
                            content = candidate.get("content", {})
                            parts = content.get("parts", [])

                            # 获取增量文本内容
                            delta_text = ""
                            for part in parts:
                                if "text" in part:
                                    delta_text += part["text"]

                            choice_data = {
                                "index": i,
                                "delta": {
                                    "role": "assistant",
                                    "content": delta_text
                                }
                            }

                            # 检查是否结束并映射结束原因
                            finish_reason = candidate.get("finishReason")
                            if finish_reason:
                                finish_reason_mapping = {
                                    "STOP": "stop",
                                    "MAX_TOKENS": "length",
                                    "SAFETY": "content_filter",
                                    "RECITATION": "content_filter",
                                    "OTHER": "stop",
                                    "BLOCKLIST": "content_filter",
                                    "PROHIBITED_CONTENT": "content_filter",
                                    "SPII": "content_filter",
                                    "MALFORMED_FUNCTION_CALL": "stop"
                                }
                                choice_data["finish_reason"] = finish_reason_mapping.get(
                                    finish_reason, "stop"
                                )

                            choices.append(choice_data)

                        # 只有在有实际内容或结束原因时才生成响应块
                        if choices and any(
                            choice.get("delta", {}).get("content") or
                            choice.get("finish_reason")
                            for choice in choices
                        ):
                            # 构建流式响应块
                            chunk_response = TextGenerationStreamChunk(
                                id=str(uuid4()),
                                object="chat.completion.chunk",
                                created=int(time.time()),
                                model=request.model,
                                choices=choices,
                                provider=self.name,
                                request_id=str(uuid4())
                            )

                            yield chunk_response

                    except json.JSONDecodeError as e:
                        # 记录解析错误但继续处理
                        self.logger.warning(f"流式响应 JSON 解析失败: {e}, 数据: {data_str}")
                        continue
                    except Exception as e:
                        # 记录其他错误
                        self.logger.error(f"处理流式响应时发生错误: {e}")
                        continue
    
    async def _generate_image_impl(
        self,
        request: ImageGenerationRequest,
        api_key: str
    ) -> ImageGenerationResponse:
        """Google AI暂不支持图像生成"""
        raise NotImplementedError("Google AI暂不支持图像生成功能")
    
    def _get_default_headers(self) -> Dict[str, str]:
        """获取默认的HTTP头"""
        return {
            "User-Agent": f"AI-Gen-Hub/1.0.0 (google_ai)",
            "Content-Type": "application/json",
        }
