"""
AI Gen Hub 核心接口定义

定义了所有AI服务的抽象基类和标准接口，确保不同供应商的实现具有一致性。
这些接口遵循SOLID原则，提供了良好的扩展性和可维护性。
"""

from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, AsyncIterator, Dict, List, Optional, Union

from pydantic import BaseModel, Field


# =============================================================================
# 枚举定义
# =============================================================================

class ModelType(str, Enum):
    """AI模型类型枚举"""
    TEXT_GENERATION = "text_generation"  # 文本生成模型
    IMAGE_GENERATION = "image_generation"  # 图像生成模型
    EMBEDDING = "embedding"  # 嵌入模型
    AUDIO_GENERATION = "audio_generation"  # 音频生成模型
    VIDEO_GENERATION = "video_generation"  # 视频生成模型


class MessageRole(str, Enum):
    """消息角色枚举"""
    SYSTEM = "system"  # 系统消息
    USER = "user"  # 用户消息
    ASSISTANT = "assistant"  # 助手消息
    FUNCTION = "function"  # 函数调用消息
    TOOL = "tool"  # 工具调用消息


class ProviderStatus(str, Enum):
    """供应商状态枚举"""
    HEALTHY = "healthy"  # 健康状态
    DEGRADED = "degraded"  # 降级状态
    UNHEALTHY = "unhealthy"  # 不健康状态
    MAINTENANCE = "maintenance"  # 维护状态


class RequestStatus(str, Enum):
    """请求状态枚举"""
    PENDING = "pending"  # 等待中
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    CANCELLED = "cancelled"  # 已取消


# =============================================================================
# 基础数据模型
# =============================================================================

class Message(BaseModel):
    """聊天消息模型"""
    role: MessageRole = Field(..., description="消息角色")
    content: str = Field(..., description="消息内容")
    name: Optional[str] = Field(None, description="消息发送者名称")
    function_call: Optional[Dict[str, Any]] = Field(None, description="函数调用信息")
    tool_calls: Optional[List[Dict[str, Any]]] = Field(None, description="工具调用信息")


class Usage(BaseModel):
    """使用量统计模型"""
    prompt_tokens: int = Field(0, description="输入token数量")
    completion_tokens: int = Field(0, description="输出token数量")
    total_tokens: int = Field(0, description="总token数量")
    thinking_tokens: Optional[int] = Field(None, description="思考过程token数量（Gemini 2.5专用）")


class ProviderInfo(BaseModel):
    """供应商信息模型"""
    name: str = Field(..., description="供应商名称")
    version: str = Field(..., description="供应商版本")
    status: ProviderStatus = Field(..., description="供应商状态")
    models: List[str] = Field(default_factory=list, description="支持的模型列表")
    capabilities: List[ModelType] = Field(default_factory=list, description="支持的能力")
    rate_limits: Dict[str, int] = Field(default_factory=dict, description="速率限制")
    last_health_check: Optional[datetime] = Field(None, description="最后健康检查时间")


# =============================================================================
# 文本生成相关模型
# =============================================================================

class TextGenerationRequest(BaseModel):
    """文本生成请求模型"""
    messages: List[Message] = Field(..., description="对话消息列表")
    model: str = Field(..., description="使用的模型名称")
    max_tokens: Optional[int] = Field(None, description="最大生成token数")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="生成温度")
    top_p: Optional[float] = Field(None, ge=0.0, le=1.0, description="核采样参数")
    top_k: Optional[int] = Field(None, ge=1, description="Top-K采样参数")
    frequency_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="频率惩罚")
    presence_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="存在惩罚")
    stop: Optional[Union[str, List[str]]] = Field(None, description="停止序列")
    stream: bool = Field(False, description="是否流式输出")
    functions: Optional[List[Dict[str, Any]]] = Field(None, description="可用函数列表")
    function_call: Optional[Union[str, Dict[str, str]]] = Field(None, description="函数调用设置")
    tools: Optional[List[Dict[str, Any]]] = Field(None, description="可用工具列表")
    tool_choice: Optional[Union[str, Dict[str, Any]]] = Field(None, description="工具选择设置")
    user: Optional[str] = Field(None, description="用户标识")

    # 结构化输出相关参数
    response_format: Optional[Dict[str, Any]] = Field(None, description="响应格式配置")
    response_schema: Optional[Dict[str, Any]] = Field(None, description="JSON Schema 约束")

    # Thinking 配置参数（Gemini 2.5 专用）
    thinking_budget: Optional[int] = Field(None, description="思考过程的 token 预算")
    thinking_config: Optional[Dict[str, Any]] = Field(None, description="思考配置")

    # 安全设置参数
    safety_settings: Optional[List[Dict[str, Any]]] = Field(None, description="安全设置配置")

    # 供应商特定参数
    provider_params: Dict[str, Any] = Field(default_factory=dict, description="供应商特定参数")


class TextGenerationChoice(BaseModel):
    """文本生成选择模型"""
    index: int = Field(..., description="选择索引")
    message: Message = Field(..., description="生成的消息")
    finish_reason: Optional[str] = Field(None, description="结束原因")


class TextGenerationResponse(BaseModel):
    """文本生成响应模型"""
    id: str = Field(..., description="响应ID")
    object: str = Field("chat.completion", description="对象类型")
    created: int = Field(..., description="创建时间戳")
    model: str = Field(..., description="使用的模型")
    choices: List[TextGenerationChoice] = Field(..., description="生成选择列表")
    usage: Optional[Usage] = Field(None, description="使用量统计")
    provider: str = Field(..., description="供应商名称")
    request_id: str = Field(..., description="请求ID")
    processing_time: float = Field(..., description="处理时间（秒）")


class TextGenerationStreamChunk(BaseModel):
    """文本生成流式响应块模型"""
    id: str = Field(..., description="响应ID")
    object: str = Field("chat.completion.chunk", description="对象类型")
    created: int = Field(..., description="创建时间戳")
    model: str = Field(..., description="使用的模型")
    choices: List[Dict[str, Any]] = Field(..., description="流式选择数据")
    provider: str = Field(..., description="供应商名称")
    request_id: str = Field(..., description="请求ID")


# =============================================================================
# 图像生成相关模型
# =============================================================================

class ImageGenerationRequest(BaseModel):
    """图像生成请求模型"""
    prompt: str = Field(..., description="图像描述提示")
    model: Optional[str] = Field(None, description="使用的模型名称")
    n: int = Field(1, ge=1, le=10, description="生成图像数量")
    size: str = Field("1024x1024", description="图像尺寸")
    quality: str = Field("standard", description="图像质量")
    style: Optional[str] = Field(None, description="图像风格")
    response_format: str = Field("url", description="响应格式")
    user: Optional[str] = Field(None, description="用户标识")

    # Gemini 图像生成特定参数
    input_images: Optional[List[str]] = Field(None, description="输入图像列表（用于图像编辑）")
    response_modalities: Optional[List[str]] = Field(None, description="响应模态（TEXT, IMAGE）")
    edit_instruction: Optional[str] = Field(None, description="图像编辑指令")

    # 供应商特定参数
    provider_params: Dict[str, Any] = Field(default_factory=dict, description="供应商特定参数")


class ImageData(BaseModel):
    """图像数据模型"""
    url: Optional[str] = Field(None, description="图像URL")
    b64_json: Optional[str] = Field(None, description="Base64编码的图像数据")
    revised_prompt: Optional[str] = Field(None, description="修订后的提示")


class ImageGenerationResponse(BaseModel):
    """图像生成响应模型"""
    created: int = Field(..., description="创建时间戳")
    data: List[ImageData] = Field(..., description="生成的图像数据")
    provider: str = Field(..., description="供应商名称")
    request_id: str = Field(..., description="请求ID")
    processing_time: float = Field(..., description="处理时间（秒）")


# =============================================================================
# 核心接口定义
# =============================================================================

class AIProvider(ABC):
    """AI供应商抽象基类
    
    定义了所有AI供应商必须实现的核心接口。每个具体的供应商适配器
    都应该继承这个基类并实现相应的方法。
    """
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """初始化供应商
        
        Args:
            name: 供应商名称
            config: 供应商配置
        """
        self.name = name
        self.config = config
        self._status = ProviderStatus.HEALTHY
        self._last_health_check: Optional[datetime] = None
    
    @property
    def status(self) -> ProviderStatus:
        """获取供应商状态"""
        return self._status
    
    @property
    def last_health_check(self) -> Optional[datetime]:
        """获取最后健康检查时间"""
        return self._last_health_check
    
    @abstractmethod
    async def initialize(self) -> None:
        """初始化供应商连接和配置"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """清理资源和连接"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """执行健康检查
        
        Returns:
            bool: 健康状态，True表示健康，False表示不健康
        """
        pass
    
    @abstractmethod
    async def get_provider_info(self) -> ProviderInfo:
        """获取供应商信息
        
        Returns:
            ProviderInfo: 供应商详细信息
        """
        pass
    
    @abstractmethod
    def supports_model_type(self, model_type: ModelType) -> bool:
        """检查是否支持指定的模型类型
        
        Args:
            model_type: 模型类型
            
        Returns:
            bool: 是否支持
        """
        pass
    
    @abstractmethod
    async def generate_text(
        self, 
        request: TextGenerationRequest
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """生成文本
        
        Args:
            request: 文本生成请求
            
        Returns:
            文本生成响应或流式响应迭代器
        """
        pass
    
    @abstractmethod
    async def generate_image(self, request: ImageGenerationRequest) -> ImageGenerationResponse:
        """生成图像
        
        Args:
            request: 图像生成请求
            
        Returns:
            ImageGenerationResponse: 图像生成响应
        """
        pass


class ProviderManager(ABC):
    """供应商管理器抽象基类
    
    负责管理多个AI供应商，提供负载均衡、故障转移等功能。
    """
    
    @abstractmethod
    async def add_provider(self, provider: AIProvider) -> None:
        """添加供应商"""
        pass
    
    @abstractmethod
    async def remove_provider(self, name: str) -> None:
        """移除供应商"""
        pass
    
    @abstractmethod
    async def get_provider(self, name: str) -> Optional[AIProvider]:
        """获取指定供应商"""
        pass
    
    @abstractmethod
    async def get_available_providers(
        self, 
        model_type: ModelType, 
        model: Optional[str] = None
    ) -> List[AIProvider]:
        """获取可用的供应商列表"""
        pass
    
    @abstractmethod
    async def select_provider(
        self, 
        model_type: ModelType, 
        model: Optional[str] = None,
        request_params: Optional[Dict[str, Any]] = None
    ) -> Optional[AIProvider]:
        """选择最佳供应商"""
        pass
