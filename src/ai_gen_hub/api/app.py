"""
AI Gen Hub FastAPI 应用

提供RESTful API接口，包括：
- 文本生成API
- 图像生成API
- 健康检查API
- 监控指标API
- WebSocket支持
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from ai_gen_hub.api.middleware import (
    AuthenticationMiddleware,
    LoggingMiddleware,
    RateLimitMiddleware,
)
from ai_gen_hub.api.routers import (
    health_router,
    image_router,
    metrics_router,
    text_router,
    websocket_router,
    debug_router,
)
from ai_gen_hub.cache import MultiLevelCache
from ai_gen_hub.config import get_settings
from ai_gen_hub.core.exceptions import AIGenHubException
from ai_gen_hub.core.logging import setup_logging, get_logger
from ai_gen_hub.monitoring import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>eal<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    metrics_collector,
)
from ai_gen_hub.services import (
    AIProviderManager,
    RequestRouter,
    TextGenerationService,
    ImageGenerationService,
)
from ai_gen_hub.utils import KeyManager


class AIGenHubApp:
    """AI Gen Hub 应用类"""
    
    def __init__(self, debug_config_loading: bool = False):
        """初始化应用

        Args:
            debug_config_loading: 是否启用配置加载调试日志
        """
        # 检查是否需要调试配置加载
        debug_env = os.environ.get('DEBUG_CONFIG_LOADING', '').lower() in ('true', '1', 'yes')
        debug_logging = debug_config_loading or debug_env

        self.settings = get_settings(debug_logging=debug_logging)
        self.logger = get_logger("app")
        
        # 核心组件
        self.key_manager: Optional[KeyManager] = None
        self.provider_manager: Optional[AIProviderManager] = None
        self.router: Optional[RequestRouter] = None
        self.cache: Optional[MultiLevelCache] = None
        self.health_manager: Optional[HealthManager] = None
        
        # 业务服务
        self.text_service: Optional[TextGenerationService] = None
        self.image_service: Optional[ImageGenerationService] = None
        
        # FastAPI应用
        self.app: Optional[FastAPI] = None
    
    async def initialize(self) -> None:
        """初始化所有组件"""
        self.logger.info("开始初始化AI Gen Hub应用")
        
        # 设置日志
        setup_logging(
            log_level=self.settings.monitoring.log_level,
            log_format=self.settings.monitoring.log_format,
            debug=self.settings.debug
        )
        
        # 初始化密钥管理器
        self.key_manager = KeyManager(self.settings)
        await self.key_manager.initialize()
        
        # 初始化供应商管理器
        self.provider_manager = AIProviderManager(self.settings, self.key_manager)
        await self.provider_manager.initialize()
        
        # 初始化请求路由器
        self.router = RequestRouter(
            self.settings,
            self.provider_manager
        )
        
        # 初始化缓存
        if self.settings.features.enable_caching:
            self.cache = MultiLevelCache(
                self.settings.cache,
                self.settings.redis
            )
        
        # 初始化业务服务
        self.text_service = TextGenerationService(
            self.settings,
            self.provider_manager,
            self.router,
            self.cache
        )
        
        self.image_service = ImageGenerationService(
            self.settings,
            self.provider_manager,
            self.router,
            self.cache
        )
        
        # 初始化健康检查
        self.health_manager = HealthManager()
        
        # 添加健康检查器
        if self.settings.database:
            # 如果配置了数据库，添加数据库健康检查
            # self.health_manager.add_checker(DatabaseHealthChecker(db_pool))
            pass
        
        # Redis健康检查
        if self.cache and hasattr(self.cache, 'l2_cache'):
            redis_client = getattr(self.cache.l2_cache, '_redis', None)
            if redis_client:
                self.health_manager.add_checker(RedisHealthChecker(redis_client))
        
        # 供应商健康检查
        self.health_manager.add_checker(ProviderHealthChecker(self.provider_manager))
        
        # 系统健康检查
        self.health_manager.add_checker(SystemHealthChecker())
        
        # 设置应用信息
        metrics_collector.set_app_info(
            version=self.settings.app_version,
            environment=self.settings.environment
        )
        
        self.logger.info("AI Gen Hub应用初始化完成")
    
    async def cleanup(self) -> None:
        """清理资源"""
        self.logger.info("开始清理AI Gen Hub应用资源")
        
        if self.provider_manager:
            await self.provider_manager.cleanup()
        
        if self.key_manager:
            await self.key_manager.cleanup()
        
        if self.cache:
            await self.cache.cleanup()
        
        self.logger.info("AI Gen Hub应用资源清理完成")
    
    def create_app(self) -> FastAPI:
        """创建FastAPI应用"""
        
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            # 启动时初始化
            await self.initialize()
            
            # 将组件注入到应用状态
            app.state.key_manager = self.key_manager
            app.state.provider_manager = self.provider_manager
            app.state.router = self.router
            app.state.cache = self.cache
            app.state.health_manager = self.health_manager
            app.state.text_service = self.text_service
            app.state.image_service = self.image_service
            app.state.settings = self.settings
            
            yield
            
            # 关闭时清理
            await self.cleanup()
        
        # 创建FastAPI应用
        app = FastAPI(
            title="AI Gen Hub",
            description="高性能AI服务聚合平台",
            version=self.settings.app_version,
            docs_url="/docs" if self.settings.debug else None,
            redoc_url="/redoc" if self.settings.debug else None,
            lifespan=lifespan
        )
        
        # 添加中间件
        self._add_middleware(app)
        
        # 添加路由
        self._add_routers(app)
        
        # 添加异常处理器
        self._add_exception_handlers(app)
        
        self.app = app
        return app
    
    def _add_middleware(self, app: FastAPI) -> None:
        """添加中间件"""
        # CORS中间件
        if self.settings.security.cors_origins:
            app.add_middleware(
                CORSMiddleware,
                allow_origins=self.settings.security.cors_origins,
                allow_credentials=self.settings.security.cors_allow_credentials,
                allow_methods=["*"],
                allow_headers=["*"],
            )
        
        # Gzip压缩中间件
        app.add_middleware(GZipMiddleware, minimum_size=1000)
        
        # 自定义中间件
        app.add_middleware(LoggingMiddleware)
        
        if self.settings.features.enable_rate_limiting:
            app.add_middleware(RateLimitMiddleware)
        
        if self.settings.security.api_key or self.settings.security.jwt_secret_key:
            app.add_middleware(AuthenticationMiddleware)
    
    def _add_routers(self, app: FastAPI) -> None:
        """添加路由"""
        # API路由
        app.include_router(
            text_router,
            prefix="/api/v1/text",
            tags=["文本生成"]
        )
        
        app.include_router(
            image_router,
            prefix="/api/v1/image",
            tags=["图像生成"]
        )
        
        # 系统路由
        app.include_router(
            health_router,
            prefix="/health",
            tags=["健康检查"]
        )
        
        app.include_router(
            metrics_router,
            prefix="/metrics",
            tags=["监控指标"]
        )
        
        # WebSocket路由
        app.include_router(
            websocket_router,
            prefix="/ws",
            tags=["WebSocket"]
        )

        # 调试路由（仅在开发环境）
        if self.settings.debug or self.settings.environment.lower() != "production":
            app.include_router(
                debug_router,
                prefix="/debug",
                tags=["调试工具"]
            )
        
        # 根路径
        @app.get("/", tags=["根路径"])
        async def root():
            """根路径信息"""
            return {
                "name": "AI Gen Hub",
                "version": self.settings.app_version,
                "environment": self.settings.environment,
                "status": "running",
                "docs_url": "/docs" if self.settings.debug else None
            }
    
    def _add_exception_handlers(self, app: FastAPI) -> None:
        """添加异常处理器"""
        
        @app.exception_handler(AIGenHubException)
        async def ai_gen_hub_exception_handler(request: Request, exc: AIGenHubException):
            """处理AI Gen Hub自定义异常"""
            return JSONResponse(
                status_code=400,
                content={
                    "error": exc.to_dict(),
                    "request_id": getattr(request.state, "request_id", None)
                }
            )
        
        @app.exception_handler(Exception)
        async def general_exception_handler(request: Request, exc: Exception):
            """处理通用异常"""
            logger = get_logger("exception_handler")
            logger.error(
                "未处理的异常",
                error=str(exc),
                error_type=type(exc).__name__,
                path=request.url.path
            )
            
            return JSONResponse(
                status_code=500,
                content={
                    "error": {
                        "error_code": "INTERNAL_SERVER_ERROR",
                        "message": "内部服务器错误",
                        "retryable": False
                    },
                    "request_id": getattr(request.state, "request_id", None)
                }
            )


# 创建全局应用实例
ai_gen_hub_app = AIGenHubApp()


def create_app() -> FastAPI:
    """创建FastAPI应用的工厂函数"""
    return ai_gen_hub_app.create_app()


def run_server(
    host: str = "0.0.0.0",
    port: int = 8000,
    workers: int = 1,
    reload: bool = False
) -> None:
    """运行服务器
    
    Args:
        host: 主机地址
        port: 端口号
        workers: 工作进程数
        reload: 是否启用热重载
    """
    uvicorn.run(
        "ai_gen_hub.api.app:create_app",
        factory=True,
        host=host,
        port=port,
        workers=workers,
        reload=reload,
        log_config=None,  # 使用我们自己的日志配置
    )


if __name__ == "__main__":
    # 直接运行时的配置
    settings = get_settings()
    run_server(
        host=settings.api_host,
        port=settings.api_port,
        workers=settings.api_workers,
        reload=settings.debug
    )
