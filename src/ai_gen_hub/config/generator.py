"""
AI Gen Hub 配置文件生成器

提供配置文件模板生成功能，支持YAML和JSON格式。
生成的配置文件包含所有可配置项的说明和默认值。
"""

import json
from typing import Any, Dict

import yaml


def generate_config_template(format: str = "yaml") -> str:
    """生成配置文件模板
    
    Args:
        format: 配置文件格式 (yaml, json)
        
    Returns:
        配置文件内容
    """
    config_template = {
        "# AI Gen Hub 配置文件": None,
        "# 请根据实际环境修改以下配置项": None,
        "": None,
        
        "# 基础配置": None,
        "environment": "development",  # 运行环境: development, staging, production
        "app_name": "AI Gen Hub",
        "app_version": "0.1.0",
        "debug": True,
        
        "# API服务配置": None,
        "api_host": "0.0.0.0",
        "api_port": 8000,
        "api_workers": 1,
        
        "# 数据库配置": None,
        "database": {
            "url": "postgresql+asyncpg://username:password@localhost:5432/ai_gen_hub",
            "pool_size": 10,
            "max_overflow": 20,
            "pool_timeout": 30,
            "echo": False
        },
        
        "# Redis配置": None,
        "redis": {
            "url": "redis://localhost:6379/0",
            "password": None,
            "db": 0,
            "pool_size": 10,
            "pool_timeout": 10,
            "decode_responses": True
        },
        
        "# AI供应商配置": None,
        "openai": {
            "api_keys": [
                "sk-your-openai-api-key-1",
                "sk-your-openai-api-key-2"
            ],
            "base_url": "https://api.openai.com/v1",
            "timeout": 60,
            "max_retries": 3,
            "retry_delay": 1.0,
            "rate_limit": 1000,  # 请求/分钟
            "enabled": True,
            "priority": 1,
            "weight": 1.0
        },
        
        "google_ai": {
            "api_keys": [
                "AIzaSy-your-google-ai-api-key-1",
                "AIzaSy-your-google-ai-api-key-2"
            ],
            "base_url": None,
            "timeout": 60,
            "max_retries": 3,
            "retry_delay": 1.0,
            "rate_limit": 500,
            "enabled": True,
            "priority": 2,
            "weight": 0.8
        },
        
        "anthropic": {
            "api_keys": [
                "sk-ant-your-anthropic-api-key-1",
                "sk-ant-your-anthropic-api-key-2"
            ],
            "base_url": "https://api.anthropic.com",
            "timeout": 60,
            "max_retries": 3,
            "retry_delay": 1.0,
            "rate_limit": 300,
            "enabled": True,
            "priority": 3,
            "weight": 0.6
        },
        
        "azure": {
            "api_keys": [
                "your-azure-openai-api-key"
            ],
            "base_url": "https://your-resource.openai.azure.com",
            "timeout": 60,
            "max_retries": 3,
            "retry_delay": 1.0,
            "rate_limit": 800,
            "enabled": False,
            "priority": 4,
            "weight": 0.9
        },
        
        "# 缓存配置": None,
        "cache": {
            "memory_cache_size": 1000,
            "memory_cache_ttl": 3600,
            "redis_cache_ttl": 7200,
            "redis_cache_prefix": "ai_gen_hub:cache:",
            "enable_memory_cache": True,
            "enable_redis_cache": True,
            "cache_compression": True
        },
        
        "# 监控配置": None,
        "monitoring": {
            "log_level": "INFO",
            "log_format": "json",
            "log_file": None,
            "prometheus_enabled": True,
            "prometheus_port": 9090,
            "health_check_interval": 60,
            "health_check_timeout": 10,
            "collect_detailed_metrics": True,
            "metrics_retention_days": 30
        },
        
        "# 安全配置": None,
        "security": {
            "jwt_secret_key": "your-super-secret-jwt-key-change-this-in-production",
            "jwt_algorithm": "HS256",
            "jwt_expire_minutes": 1440,
            "api_key": "your-api-key-here",
            "cors_origins": [
                "http://localhost:3000",
                "http://localhost:8080"
            ],
            "cors_allow_credentials": True
        },
        
        "# 存储配置": None,
        "storage": {
            "local_storage_path": "./storage",
            "s3_bucket": None,
            "s3_region": "us-east-1",
            "s3_access_key": None,
            "s3_secret_key": None,
            "s3_endpoint_url": None,
            "minio_endpoint": "localhost:9000",
            "minio_access_key": "minioadmin",
            "minio_secret_key": "minioadmin",
            "minio_bucket": "ai-gen-hub",
            "minio_secure": False
        },
        
        "# 性能配置": None,
        "performance": {
            "rate_limit_requests": 100,
            "rate_limit_window": 60,
            "request_timeout": 300,
            "stream_timeout": 600,
            "max_concurrent_requests": 100,
            "max_queue_size": 1000,
            "default_max_retries": 3,
            "default_retry_delay": 1.0,
            "exponential_backoff": True
        },
        
        "# 功能开关": None,
        "features": {
            "enable_text_generation": True,
            "enable_image_generation": True,
            "enable_streaming": True,
            "enable_caching": True,
            "enable_monitoring": True,
            "enable_rate_limiting": True,
            "enable_load_balancing": True,
            "enable_circuit_breaker": True
        }
    }
    
    # 移除注释键（以#开头的键）
    clean_config = {}
    for key, value in config_template.items():
        if not key.startswith("#") and key != "":
            clean_config[key] = value
    
    if format.lower() == "json":
        return json.dumps(clean_config, indent=2, ensure_ascii=False)
    else:
        # 生成带注释的YAML
        return _generate_yaml_with_comments(config_template)


def _generate_yaml_with_comments(config: Dict[str, Any]) -> str:
    """生成带注释的YAML配置
    
    Args:
        config: 配置字典
        
    Returns:
        YAML格式的配置字符串
    """
    lines = []
    lines.append("# AI Gen Hub 配置文件")
    lines.append("# 请根据实际环境修改以下配置项")
    lines.append("")
    
    current_section = None
    
    for key, value in config.items():
        if key.startswith("#"):
            # 注释行
            if current_section is not None:
                lines.append("")  # 在新节之前添加空行
            lines.append(key)
            current_section = key
        elif key == "":
            # 空行
            continue
        else:
            # 配置项
            if isinstance(value, dict):
                lines.append(f"{key}:")
                for sub_key, sub_value in value.items():
                    if isinstance(sub_value, list):
                        lines.append(f"  {sub_key}:")
                        for item in sub_value:
                            if isinstance(item, str) and ("key" in sub_key.lower() or "secret" in sub_key.lower()):
                                lines.append(f"    - \"{item}\"")
                            else:
                                lines.append(f"    - {item}")
                    elif isinstance(sub_value, str):
                        if sub_value is None:
                            lines.append(f"  {sub_key}: null")
                        else:
                            lines.append(f"  {sub_key}: \"{sub_value}\"")
                    else:
                        lines.append(f"  {sub_key}: {sub_value}")
            elif isinstance(value, list):
                lines.append(f"{key}:")
                for item in value:
                    lines.append(f"  - {item}")
            elif isinstance(value, str):
                lines.append(f"{key}: \"{value}\"")
            else:
                lines.append(f"{key}: {value}")
    
    return "\n".join(lines)


def generate_env_template() -> str:
    """生成环境变量模板
    
    Returns:
        .env文件内容
    """
    env_template = """# AI Gen Hub 环境变量配置
# 复制此文件为 .env 并填入实际的配置值

# =============================================================================
# 基础配置
# =============================================================================

ENVIRONMENT=development
APP_NAME=AI Gen Hub
APP_VERSION=0.1.0
DEBUG=true

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# =============================================================================
# 数据库配置
# =============================================================================

DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/ai_gen_hub
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# =============================================================================
# Redis配置
# =============================================================================

REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0
REDIS_POOL_SIZE=10
REDIS_POOL_TIMEOUT=10

# =============================================================================
# AI供应商配置
# =============================================================================

# OpenAI配置
OPENAI_API_KEYS=sk-xxx,sk-yyy,sk-zzz
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_TIMEOUT=60
OPENAI_MAX_RETRIES=3

# Google AI配置
GOOGLE_AI_API_KEYS=AIzaSyXXX,AIzaSyYYY
GOOGLE_AI_TIMEOUT=60
GOOGLE_AI_MAX_RETRIES=3

# Anthropic配置
ANTHROPIC_API_KEYS=sk-ant-xxx,sk-ant-yyy
ANTHROPIC_BASE_URL=https://api.anthropic.com
ANTHROPIC_TIMEOUT=60
ANTHROPIC_MAX_RETRIES=3

# =============================================================================
# 缓存配置
# =============================================================================

MEMORY_CACHE_SIZE=1000
MEMORY_CACHE_TTL=3600
REDIS_CACHE_TTL=7200
REDIS_CACHE_PREFIX=ai_gen_hub:cache:

# =============================================================================
# 监控配置
# =============================================================================

LOG_LEVEL=INFO
LOG_FORMAT=json
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# =============================================================================
# 安全配置
# =============================================================================

JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440
API_KEY=your-api-key-here
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# =============================================================================
# 存储配置
# =============================================================================

LOCAL_STORAGE_PATH=./storage
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=ai-gen-hub

# =============================================================================
# 性能配置
# =============================================================================

RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
REQUEST_TIMEOUT=300
MAX_CONCURRENT_REQUESTS=100

# =============================================================================
# 功能开关
# =============================================================================

ENABLE_TEXT_GENERATION=true
ENABLE_IMAGE_GENERATION=true
ENABLE_STREAMING=true
ENABLE_CACHING=true
ENABLE_MONITORING=true
"""
    
    return env_template.strip()
