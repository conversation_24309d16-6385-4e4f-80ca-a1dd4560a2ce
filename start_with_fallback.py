#!/usr/bin/env python3
"""
AI Gen Hub 容错启动脚本

自动处理Redis兼容性问题，提供优雅降级
"""

import asyncio
import os
import sys
import time
from pathlib import Path
from typing import Optional

import click

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def check_redis_compatibility():
    """检查Redis兼容性"""
    print("🔍 检查Redis兼容性...")
    
    try:
        # 尝试导入aioredis
        import aioredis
        from aioredis.exceptions import TimeoutError
        print("✅ aioredis兼容性正常")
        return "aioredis"
    except TypeError as e:
        if "duplicate base class TimeoutError" in str(e):
            print("⚠️  检测到aioredis TimeoutError冲突")
            return "conflict"
        else:
            print(f"❌ aioredis导入错误: {e}")
            return "error"
    except ImportError:
        print("📦 aioredis未安装")
        return "missing"
    except Exception as e:
        print(f"❌ aioredis检查失败: {e}")
        return "error"

def check_redis_py():
    """检查redis-py可用性"""
    try:
        import redis.asyncio
        print("✅ redis-py异步支持可用")
        return True
    except ImportError:
        try:
            import redis
            print("⚠️  redis-py可用但不支持异步")
            return False
        except ImportError:
            print("❌ redis-py不可用")
            return False

def fix_redis_issues():
    """修复Redis问题"""
    print("🔧 修复Redis兼容性问题...")
    
    redis_status = check_redis_compatibility()
    redis_py_available = check_redis_py()
    
    if redis_status == "conflict":
        print("🔧 处理aioredis TimeoutError冲突...")
        
        if not redis_py_available:
            print("📥 安装redis-py...")
            import subprocess
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "--upgrade", "redis>=5.0.1"
                ], check=True, capture_output=True)
                print("✅ redis-py安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ redis-py安装失败: {e}")
                return False
        
        # 设置环境变量以使用兼容模式
        os.environ['USE_REDIS_COMPAT'] = 'true'
        print("✅ 已启用Redis兼容模式")
        return True
    
    elif redis_status in ["missing", "error"]:
        print("⚠️  Redis库不可用，将仅使用内存缓存")
        os.environ['DISABLE_REDIS'] = 'true'
        return True
    
    else:
        print("✅ Redis兼容性正常")
        return True

async def start_ai_gen_hub_safe():
    """安全启动AI Gen Hub"""
    print("🚀 安全启动AI Gen Hub...")
    
    try:
        # 修复Redis问题
        if not fix_redis_issues():
            print("❌ Redis问题修复失败")
            return False
        
        # 设置环境变量
        os.environ.setdefault('ENVIRONMENT', 'development')
        os.environ.setdefault('DEBUG', 'true')
        os.environ.setdefault('API_PORT', '8001')
        
        # 导入AI Gen Hub模块
        try:
            from ai_gen_hub.api.app import AIGenHubApp
            
            # 创建应用实例
            app_instance = AIGenHubApp()
            
            # 初始化应用
            await app_instance.initialize()
            
            print("✅ AI Gen Hub初始化成功")
            
            # 创建FastAPI应用
            app = app_instance.create_app()
            
            print("✅ FastAPI应用创建成功")
            
            # 启动服务器
            import uvicorn
            
            config = uvicorn.Config(
                app=app,
                host="0.0.0.0",
                port=8001,
                log_level="info",
                reload=False
            )
            
            server = uvicorn.Server(config)
            
            print("🌟 AI Gen Hub服务启动成功!")
            print("📍 API地址: http://localhost:8001")
            print("📍 API文档: http://localhost:8001/docs")
            print("📍 健康检查: http://localhost:8001/health")
            
            await server.serve()
            
        except ImportError as e:
            print(f"❌ AI Gen Hub模块导入失败: {e}")
            print("💡 请先运行: ./fix_module_import.sh")
            return False
        
        except Exception as e:
            print(f"❌ AI Gen Hub启动失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
        return True
    
    except Exception as e:
        print(f"❌ 启动过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def start_debug_page():
    """启动调试页面"""
    print("🚀 启动调试页面...")
    
    try:
        # 导入调试页面
        import debug_standalone
        
        # 获取应用实例
        app = debug_standalone.app
        
        # 启动服务器
        import uvicorn
        
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            reload=False
        )
        
        server = uvicorn.Server(config)
        
        print("🌟 调试页面启动成功!")
        print("📍 调试页面: http://localhost:8000")
        
        await server.serve()
        
    except Exception as e:
        print(f"❌ 调试页面启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

@click.group()
def cli():
    """AI Gen Hub 容错启动工具"""
    pass

@cli.command()
def serve():
    """启动AI Gen Hub服务"""
    print("🌟 AI Gen Hub 容错启动")
    print("=" * 50)
    
    success = asyncio.run(start_ai_gen_hub_safe())
    if not success:
        print("❌ 服务启动失败")
        sys.exit(1)

@cli.command()
def debug():
    """启动调试页面"""
    print("🌟 AI Gen Hub 调试页面")
    print("=" * 50)
    
    success = asyncio.run(start_debug_page())
    if not success:
        print("❌ 调试页面启动失败")
        sys.exit(1)

@cli.command()
def both():
    """同时启动服务和调试页面"""
    print("🌟 AI Gen Hub 完整环境")
    print("=" * 50)
    
    async def start_both():
        # 启动AI Gen Hub服务（后台）
        import subprocess
        
        print("🚀 启动AI Gen Hub服务...")
        ai_gen_hub_process = subprocess.Popen([
            sys.executable, __file__, "serve"
        ])
        
        # 等待服务启动
        await asyncio.sleep(3)
        
        print("🚀 启动调试页面...")
        try:
            await start_debug_page()
        finally:
            # 清理AI Gen Hub进程
            ai_gen_hub_process.terminate()
            ai_gen_hub_process.wait()
    
    try:
        asyncio.run(start_both())
    except KeyboardInterrupt:
        print("\n👋 所有服务已停止")

@cli.command()
def check():
    """检查环境和依赖"""
    print("🔍 环境检查")
    print("=" * 50)
    
    # 检查Python版本
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    # 检查Redis兼容性
    redis_status = check_redis_compatibility()
    redis_py_available = check_redis_py()
    
    print(f"📦 Redis状态: {redis_status}")
    print(f"📦 redis-py可用: {redis_py_available}")
    
    # 检查AI Gen Hub模块
    try:
        import ai_gen_hub
        print("✅ AI Gen Hub模块可用")
    except ImportError:
        print("❌ AI Gen Hub模块不可用")
        print("💡 请运行: ./fix_module_import.sh")
    
    # 检查配置文件
    env_file = Path(".env")
    if env_file.exists():
        print("✅ 配置文件 .env 存在")
    else:
        print("⚠️  配置文件 .env 不存在")
        print("💡 请创建 .env 文件并配置API密钥")

if __name__ == "__main__":
    cli()
