#!/usr/bin/env python3
"""
AI Gen Hub 完整启动脚本

同时启动AI Gen Hub服务和调试页面，提供完整的开发环境
"""

import asyncio
import os
import signal
import subprocess
import sys
import time
from pathlib import Path
from typing import Optional

import click
import httpx

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

# 配置
AI_GEN_HUB_PORT = 8001
DEBUG_PORT = 8000
STARTUP_TIMEOUT = 30  # 服务启动超时时间（秒）

class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.ai_gen_hub_process: Optional[subprocess.Popen] = None
        self.debug_process: Optional[subprocess.Popen] = None
        self.running = False
    
    async def start_ai_gen_hub(self):
        """启动AI Gen Hub服务"""
        print("🚀 启动AI Gen Hub服务...")
        
        # 设置环境变量
        env = os.environ.copy()
        env.update({
            'ENVIRONMENT': 'development',
            'DEBUG': 'true',
            'API_PORT': str(AI_GEN_HUB_PORT),
            'PYTHONPATH': str(src_path)
        })
        
        try:
            # 使用run_server.py启动
            self.ai_gen_hub_process = subprocess.Popen([
                sys.executable, 
                "run_server.py",
                "serve",
                "--host", "0.0.0.0",
                "--port", str(AI_GEN_HUB_PORT),
                "--reload"
            ], env=env, cwd=project_root)
            
            # 等待服务启动
            if await self.wait_for_service(f"http://localhost:{AI_GEN_HUB_PORT}/health"):
                print(f"✅ AI Gen Hub服务已启动 (端口 {AI_GEN_HUB_PORT})")
                return True
            else:
                print("❌ AI Gen Hub服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动AI Gen Hub服务失败: {e}")
            return False
    
    async def start_debug_page(self):
        """启动调试页面"""
        print("🚀 启动调试页面...")
        
        try:
            # 启动调试页面
            self.debug_process = subprocess.Popen([
                sys.executable, 
                "debug_standalone.py"
            ], cwd=project_root)
            
            # 等待服务启动
            if await self.wait_for_service(f"http://localhost:{DEBUG_PORT}/"):
                print(f"✅ 调试页面已启动 (端口 {DEBUG_PORT})")
                return True
            else:
                print("❌ 调试页面启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动调试页面失败: {e}")
            return False
    
    async def wait_for_service(self, url: str, timeout: int = STARTUP_TIMEOUT) -> bool:
        """等待服务启动"""
        print(f"⏳ 等待服务启动: {url}")
        
        async with httpx.AsyncClient() as client:
            for i in range(timeout):
                try:
                    response = await client.get(url, timeout=1.0)
                    if response.status_code < 500:
                        return True
                except:
                    pass
                
                await asyncio.sleep(1)
                if i % 5 == 0:
                    print(f"   等待中... ({i}/{timeout})")
        
        return False
    
    def stop_services(self):
        """停止所有服务"""
        print("🛑 停止服务...")
        
        if self.debug_process:
            try:
                self.debug_process.terminate()
                self.debug_process.wait(timeout=5)
                print("✅ 调试页面已停止")
            except:
                self.debug_process.kill()
                print("🔪 强制停止调试页面")
        
        if self.ai_gen_hub_process:
            try:
                self.ai_gen_hub_process.terminate()
                self.ai_gen_hub_process.wait(timeout=5)
                print("✅ AI Gen Hub服务已停止")
            except:
                self.ai_gen_hub_process.kill()
                print("🔪 强制停止AI Gen Hub服务")
        
        self.running = False
    
    async def run(self):
        """运行服务管理器"""
        self.running = True
        
        # 设置信号处理
        def signal_handler(signum, frame):
            print(f"\n📡 收到信号 {signum}，正在停止服务...")
            self.stop_services()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # 启动AI Gen Hub服务
            ai_gen_hub_started = await self.start_ai_gen_hub()
            
            # 启动调试页面
            debug_started = await self.start_debug_page()
            
            if ai_gen_hub_started and debug_started:
                print("\n🎉 所有服务启动成功！")
                print(f"📍 AI Gen Hub API: http://localhost:{AI_GEN_HUB_PORT}")
                print(f"📍 调试仪表板: http://localhost:{DEBUG_PORT}")
                print(f"📍 API文档: http://localhost:{AI_GEN_HUB_PORT}/docs")
                print("\n按 Ctrl+C 停止所有服务")
                
                # 保持运行
                while self.running:
                    await asyncio.sleep(1)
                    
                    # 检查进程状态
                    if self.ai_gen_hub_process and self.ai_gen_hub_process.poll() is not None:
                        print("⚠️ AI Gen Hub服务意外停止")
                        break
                    
                    if self.debug_process and self.debug_process.poll() is not None:
                        print("⚠️ 调试页面意外停止")
                        break
            else:
                print("❌ 服务启动失败")
                self.stop_services()
                return 1
                
        except Exception as e:
            print(f"❌ 运行错误: {e}")
            self.stop_services()
            return 1
        
        return 0

@click.command()
@click.option("--ai-port", default=AI_GEN_HUB_PORT, help="AI Gen Hub服务端口")
@click.option("--debug-port", default=DEBUG_PORT, help="调试页面端口")
@click.option("--timeout", default=STARTUP_TIMEOUT, help="启动超时时间")
def main(ai_port: int, debug_port: int, timeout: int):
    """启动AI Gen Hub完整开发环境"""
    global AI_GEN_HUB_PORT, DEBUG_PORT, STARTUP_TIMEOUT
    AI_GEN_HUB_PORT = ai_port
    DEBUG_PORT = debug_port
    STARTUP_TIMEOUT = timeout
    
    print("🌟 AI Gen Hub 完整开发环境")
    print("=" * 50)
    
    # 检查依赖
    print("🔍 检查环境...")
    
    # 检查Python路径
    if not src_path.exists():
        print(f"❌ 源代码目录不存在: {src_path}")
        sys.exit(1)
    
    # 检查关键文件
    required_files = [
        "run_server.py",
        "debug_standalone.py",
        "src/ai_gen_hub/main.py"
    ]
    
    for file_path in required_files:
        if not (project_root / file_path).exists():
            print(f"❌ 必需文件不存在: {file_path}")
            sys.exit(1)
    
    print("✅ 环境检查通过")
    
    # 启动服务管理器
    manager = ServiceManager()
    exit_code = asyncio.run(manager.run())
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
