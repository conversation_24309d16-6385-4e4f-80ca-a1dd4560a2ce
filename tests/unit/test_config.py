"""
配置管理模块单元测试
"""

import os
import tempfile
import pytest
from unittest.mock import patch

from ai_gen_hub.config import (
    Settings,
    get_settings,
    reload_settings,
    generate_config_template,
    generate_env_template,
)


@pytest.mark.unit
class TestSettings:
    """设置类测试"""
    
    def test_default_settings(self):
        """测试默认设置"""
        settings = Settings()
        
        assert settings.environment == "development"
        assert settings.app_name == "AI Gen Hub"
        assert settings.debug is True
        assert settings.api_host == "0.0.0.0"
        assert settings.api_port == 8000
    
    def test_settings_validation(self):
        """测试设置验证"""
        # 测试有效设置
        settings = Settings(
            api_port=9000,
            openai__api_keys=["sk-test123"],
            openai__enabled=True
        )
        assert settings.api_port == 9000
        assert settings.openai.api_keys == ["sk-test123"]
        assert settings.openai.enabled is True
    
    def test_invalid_settings(self):
        """测试无效设置"""
        with pytest.raises(ValueError):
            Settings(api_port=-1)  # 端口号不能为负数
        
        with pytest.raises(ValueError):
            Settings(api_port=70000)  # 端口号不能超过65535
    
    def test_environment_variables(self):
        """测试环境变量"""
        with patch.dict(os.environ, {
            'ENVIRONMENT': 'production',
            'API_PORT': '9000',
            'DEBUG': 'false',
            'OPENAI_API_KEYS': 'sk-test1,sk-test2',
        }):
            settings = Settings()
            
            assert settings.environment == "production"
            assert settings.api_port == 9000
            assert settings.debug is False
            assert settings.openai.api_keys == ["sk-test1", "sk-test2"]
    
    def test_nested_config(self):
        """测试嵌套配置"""
        settings = Settings(
            database__url="postgresql://test:test@localhost/test",
            redis__url="redis://localhost:6379/1",
            cache__memory_cache_size=500
        )
        
        assert settings.database.url == "postgresql://test:test@localhost/test"
        assert settings.redis.url == "redis://localhost:6379/1"
        assert settings.cache.memory_cache_size == 500


@pytest.mark.unit
class TestConfigManagement:
    """配置管理测试"""
    
    def test_get_settings_singleton(self):
        """测试设置单例"""
        settings1 = get_settings()
        settings2 = get_settings()
        
        assert settings1 is settings2
    
    def test_reload_settings(self):
        """测试重新加载设置"""
        # 获取初始设置
        initial_settings = get_settings()
        initial_port = initial_settings.api_port
        
        # 重新加载设置
        with patch.dict(os.environ, {'API_PORT': '9999'}):
            new_settings = reload_settings()
            
            assert new_settings.api_port == 9999
            assert new_settings is not initial_settings
    
    def test_config_from_file(self):
        """测试从文件加载配置"""
        config_content = """
environment: test
api_port: 8888
debug: false
openai:
  api_keys:
    - sk-test123
  enabled: true
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(config_content)
            f.flush()
            
            try:
                with patch.dict(os.environ, {'CONFIG_FILE': f.name}):
                    settings = Settings()
                    
                    assert settings.environment == "test"
                    assert settings.api_port == 8888
                    assert settings.debug is False
                    assert settings.openai.api_keys == ["sk-test123"]
                    assert settings.openai.enabled is True
            finally:
                os.unlink(f.name)


@pytest.mark.unit
class TestConfigGeneration:
    """配置生成测试"""
    
    def test_generate_yaml_config(self):
        """测试生成YAML配置"""
        yaml_config = generate_config_template("yaml")
        
        assert isinstance(yaml_config, str)
        assert "environment:" in yaml_config
        assert "api_host:" in yaml_config
        assert "openai:" in yaml_config
        assert "# AI Gen Hub 配置文件" in yaml_config
    
    def test_generate_json_config(self):
        """测试生成JSON配置"""
        json_config = generate_config_template("json")
        
        assert isinstance(json_config, str)
        assert '"environment"' in json_config
        assert '"api_host"' in json_config
        assert '"openai"' in json_config
        
        # 验证是否为有效JSON
        import json
        config_dict = json.loads(json_config)
        assert isinstance(config_dict, dict)
        assert "environment" in config_dict
    
    def test_generate_env_template(self):
        """测试生成环境变量模板"""
        env_template = generate_env_template()
        
        assert isinstance(env_template, str)
        assert "ENVIRONMENT=" in env_template
        assert "API_HOST=" in env_template
        assert "OPENAI_API_KEYS=" in env_template
        assert "# AI Gen Hub 环境变量配置" in env_template


@pytest.mark.unit
class TestProviderConfig:
    """供应商配置测试"""
    
    def test_openai_config(self):
        """测试OpenAI配置"""
        settings = Settings(
            openai__api_keys=["sk-test1", "sk-test2"],
            openai__base_url="https://api.openai.com/v1",
            openai__timeout=30,
            openai__max_retries=5,
            openai__enabled=True
        )
        
        assert len(settings.openai.api_keys) == 2
        assert settings.openai.base_url == "https://api.openai.com/v1"
        assert settings.openai.timeout == 30
        assert settings.openai.max_retries == 5
        assert settings.openai.enabled is True
    
    def test_google_ai_config(self):
        """测试Google AI配置"""
        settings = Settings(
            google_ai__api_keys=["AIzaSy-test"],
            google_ai__timeout=45,
            google_ai__enabled=True
        )
        
        assert settings.google_ai.api_keys == ["AIzaSy-test"]
        assert settings.google_ai.timeout == 45
        assert settings.google_ai.enabled is True
    
    def test_anthropic_config(self):
        """测试Anthropic配置"""
        settings = Settings(
            anthropic__api_keys=["sk-ant-test"],
            anthropic__base_url="https://api.anthropic.com",
            anthropic__enabled=True
        )
        
        assert settings.anthropic.api_keys == ["sk-ant-test"]
        assert settings.anthropic.base_url == "https://api.anthropic.com"
        assert settings.anthropic.enabled is True


@pytest.mark.unit
class TestCacheConfig:
    """缓存配置测试"""
    
    def test_cache_config(self):
        """测试缓存配置"""
        settings = Settings(
            cache__enable_memory_cache=True,
            cache__enable_redis_cache=False,
            cache__memory_cache_size=2000,
            cache__memory_cache_ttl=1800,
            cache__redis_cache_ttl=3600
        )
        
        assert settings.cache.enable_memory_cache is True
        assert settings.cache.enable_redis_cache is False
        assert settings.cache.memory_cache_size == 2000
        assert settings.cache.memory_cache_ttl == 1800
        assert settings.cache.redis_cache_ttl == 3600


@pytest.mark.unit
class TestFeatureFlags:
    """功能开关测试"""
    
    def test_feature_flags(self):
        """测试功能开关"""
        settings = Settings(
            features__enable_text_generation=True,
            features__enable_image_generation=False,
            features__enable_streaming=True,
            features__enable_caching=True,
            features__enable_monitoring=False
        )
        
        assert settings.features.enable_text_generation is True
        assert settings.features.enable_image_generation is False
        assert settings.features.enable_streaming is True
        assert settings.features.enable_caching is True
        assert settings.features.enable_monitoring is False
