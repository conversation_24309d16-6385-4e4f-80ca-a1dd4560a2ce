"""
AI Gen Hub 调试仪表板测试

测试调试页面的各项功能，确保在不同环境下的正确行为
"""

import pytest
import json
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from fastapi import FastAPI

# 模拟设置类
class MockSettings:
    def __init__(self, environment="development", debug=True):
        self.app_name = "AI Gen Hub Test"
        self.app_version = "0.1.0"
        self.environment = environment
        self.debug = debug
        self.api_host = "localhost"
        self.api_port = 8000

def create_test_app(environment="development", debug=True):
    """创建测试应用"""
    app = FastAPI()
    
    # 设置应用状态
    app.state.settings = MockSettings(environment, debug)
    app.state.start_time = 1640995200.0  # 固定时间戳
    
    # 导入并添加调试路由
    from src.ai_gen_hub.api.routers.debug import router as debug_router
    app.include_router(debug_router, prefix="/debug")
    
    return app

class TestDebugAccess:
    """测试调试页面访问控制"""
    
    def test_access_allowed_in_development(self):
        """测试开发环境允许访问"""
        app = create_test_app(environment="development", debug=True)
        client = TestClient(app)
        
        response = client.get("/debug/")
        assert response.status_code == 200
    
    def test_access_denied_in_production(self):
        """测试生产环境拒绝访问"""
        app = create_test_app(environment="production", debug=True)
        client = TestClient(app)
        
        response = client.get("/debug/")
        assert response.status_code == 403
        assert "生产环境" in response.json()["detail"]
    
    def test_access_denied_when_debug_disabled(self):
        """测试调试模式禁用时拒绝访问"""
        app = create_test_app(environment="development", debug=False)
        client = TestClient(app)
        
        response = client.get("/debug/")
        assert response.status_code == 403
        assert "调试模式" in response.json()["detail"]

class TestSystemAPI:
    """测试系统监控API"""
    
    def test_system_info_api(self):
        """测试系统信息API"""
        app = create_test_app()
        client = TestClient(app)
        
        response = client.get("/debug/api/system/info")
        assert response.status_code == 200
        
        data = response.json()
        assert "system" in data
        assert "application" in data
        assert "health" in data
        assert "timestamp" in data
        
        # 检查系统信息结构
        system = data["system"]
        assert "cpu_percent" in system
        assert "memory_percent" in system
        assert "disk_percent" in system
        assert "uptime" in system
    
    @patch('src.ai_gen_hub.api.routers.debug.PSUTIL_AVAILABLE', False)
    def test_system_info_without_psutil(self):
        """测试没有psutil时的系统信息"""
        app = create_test_app()
        client = TestClient(app)
        
        response = client.get("/debug/api/system/info")
        assert response.status_code == 200
        
        data = response.json()
        system = data["system"]
        # 没有psutil时应该返回0值
        assert system["cpu_percent"] == 0.0
        assert system["memory_percent"] == 0.0

class TestEndpointsAPI:
    """测试API端点发现"""
    
    def test_endpoints_discovery(self):
        """测试端点发现功能"""
        app = create_test_app()
        
        # 添加一些测试端点
        @app.get("/test")
        async def test_endpoint():
            return {"test": "ok"}
        
        @app.post("/echo")
        async def echo_endpoint(data: dict):
            return data
        
        client = TestClient(app)
        response = client.get("/debug/api/endpoints")
        assert response.status_code == 200
        
        data = response.json()
        assert "endpoints" in data
        assert "total" in data
        
        # 检查是否包含测试端点
        endpoints = data["endpoints"]
        paths = [ep["path"] for ep in endpoints]
        assert "/test" in paths
        assert "/echo" in paths

class TestAPITesting:
    """测试API测试功能"""
    
    def test_api_test_execution(self):
        """测试API测试执行"""
        app = create_test_app()
        
        # 添加测试端点
        @app.get("/api/test")
        async def test_api():
            return {"message": "success", "status": "ok"}
        
        client = TestClient(app)
        
        # 测试GET请求
        test_data = {
            "url": "/api/test",
            "method": "GET",
            "headers": {},
            "params": {},
            "body": ""
        }
        
        response = client.post("/debug/api/test-endpoint", json=test_data)
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        assert "response" in result
        assert "timing" in result
        
        # 检查响应信息
        assert result["response"]["status_code"] == 200
        assert "response_time" in result["timing"]
    
    def test_api_test_with_json_body(self):
        """测试带JSON请求体的API测试"""
        app = create_test_app()
        
        @app.post("/api/echo")
        async def echo_api(data: dict):
            return {"echo": data}
        
        client = TestClient(app)
        
        test_data = {
            "url": "/api/echo",
            "method": "POST",
            "headers": {"Content-Type": "application/json"},
            "params": {},
            "body": '{"test": "data"}'
        }
        
        response = client.post("/debug/api/test-endpoint", json=test_data)
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        
        # 检查响应体
        response_json = result["response"]["json"]
        assert response_json["echo"]["test"] == "data"

class TestLogsAPI:
    """测试日志API"""
    
    def test_logs_api(self):
        """测试日志获取API"""
        app = create_test_app()
        client = TestClient(app)
        
        response = client.get("/debug/api/logs")
        assert response.status_code == 200
        
        data = response.json()
        assert "logs" in data
        assert "total" in data
        assert "timestamp" in data
    
    def test_logs_filtering(self):
        """测试日志过滤功能"""
        app = create_test_app()
        client = TestClient(app)
        
        # 测试按级别过滤
        response = client.get("/debug/api/logs?level=ERROR&limit=10")
        assert response.status_code == 200
        
        data = response.json()
        assert data["level_filter"] == "ERROR"
    
    def test_logs_levels(self):
        """测试日志级别API"""
        app = create_test_app()
        client = TestClient(app)
        
        response = client.get("/debug/api/logs/levels")
        assert response.status_code == 200
        
        data = response.json()
        assert "levels" in data
        
        # 检查标准日志级别
        level_names = [level["name"] for level in data["levels"]]
        assert "DEBUG" in level_names
        assert "INFO" in level_names
        assert "ERROR" in level_names

class TestConfigAPI:
    """测试配置API"""
    
    def test_config_api(self):
        """测试配置获取API"""
        app = create_test_app()
        client = TestClient(app)
        
        response = client.get("/debug/api/config")
        assert response.status_code == 200
        
        data = response.json()
        assert "config" in data
        assert "timestamp" in data
        assert "note" in data
        
        # 检查配置结构
        config = data["config"]
        assert "application" in config
        assert config["application"]["name"] == "AI Gen Hub Test"
    
    def test_environment_variables(self):
        """测试环境变量API"""
        app = create_test_app()
        client = TestClient(app)
        
        response = client.get("/debug/api/config/environment")
        assert response.status_code == 200
        
        data = response.json()
        assert "application_vars" in data
        assert "system_vars" in data
        assert "total_env_vars" in data
    
    def test_runtime_info(self):
        """测试运行时信息API"""
        app = create_test_app()
        client = TestClient(app)
        
        response = client.get("/debug/api/config/runtime")
        assert response.status_code == 200
        
        data = response.json()
        assert "runtime" in data
        
        runtime = data["runtime"]
        assert "python" in runtime
        assert "system" in runtime
        assert "packages" in runtime

class TestDataMasking:
    """测试数据脱敏功能"""
    
    def test_sensitive_data_masking(self):
        """测试敏感数据脱敏"""
        from src.ai_gen_hub.api.routers.debug import mask_sensitive_value
        
        # 测试密码脱敏
        masked = mask_sensitive_value("password", "secret123")
        assert masked == "***123"
        
        # 测试API密钥脱敏
        masked = mask_sensitive_value("api_key", "sk-1234567890abcdef")
        assert masked == "***cdef"
        
        # 测试普通值不脱敏
        masked = mask_sensitive_value("username", "testuser")
        assert masked == "testuser"
        
        # 测试空值处理
        masked = mask_sensitive_value("password", "")
        assert masked == "***"

class TestErrorHandling:
    """测试错误处理"""
    
    def test_invalid_api_test(self):
        """测试无效的API测试请求"""
        app = create_test_app()
        client = TestClient(app)
        
        # 测试无效的URL
        test_data = {
            "url": "/nonexistent",
            "method": "GET",
            "headers": {},
            "params": {},
            "body": ""
        }
        
        response = client.post("/debug/api/test-endpoint", json=test_data)
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is False
        assert "error" in result
    
    def test_malformed_json_body(self):
        """测试格式错误的JSON请求体"""
        app = create_test_app()
        client = TestClient(app)
        
        test_data = {
            "url": "/api/test",
            "method": "POST",
            "headers": {"Content-Type": "application/json"},
            "params": {},
            "body": "invalid json"
        }
        
        response = client.post("/debug/api/test-endpoint", json=test_data)
        assert response.status_code == 200
        
        result = response.json()
        # 应该作为文本内容发送，而不是JSON
        assert result["success"] is True or "error" in result

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
