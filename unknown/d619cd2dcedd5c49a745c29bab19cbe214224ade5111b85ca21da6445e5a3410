"""
AI Gen Hub 文本生成API路由

提供文本生成相关的API端点，包括：
- 文本生成
- 流式文本生成
- 支持的模型查询
"""

from typing import Dict, List, Union

from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse

from ai_gen_hub.core.interfaces import (
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
)
from ai_gen_hub.core.exceptions import AIGenHubException
from ai_gen_hub.services import TextGenerationService


router = APIRouter()


def get_text_service(request: Request) -> TextGenerationService:
    """获取文本生成服务依赖"""
    service = getattr(request.app.state, "text_service", None)
    if not service:
        raise HTTPException(status_code=500, detail="文本生成服务未初始化")
    return service


def get_user_id(request: Request) -> str:
    """获取用户ID依赖"""
    user = getattr(request.state, "user", {})
    return user.get("user_id", "anonymous")


@router.post("/generate", response_model=Union[TextGenerationResponse, None])
async def generate_text(
    request_data: TextGenerationRequest,
    request: Request,
    text_service: TextGenerationService = Depends(get_text_service),
    user_id: str = Depends(get_user_id)
):
    """生成文本
    
    支持同步和流式两种模式：
    - 同步模式：返回完整的文本生成结果
    - 流式模式：返回Server-Sent Events流
    """
    try:
        if request_data.stream:
            # 流式响应
            async def generate_stream():
                async for chunk in await text_service.generate_text(
                    request_data,
                    user_id=user_id,
                    request_id=getattr(request.state, "request_id", None)
                ):
                    yield {
                        "event": "chunk",
                        "data": chunk.json()
                    }
                
                # 发送结束事件
                yield {
                    "event": "done",
                    "data": "[DONE]"
                }
            
            return EventSourceResponse(generate_stream())
        else:
            # 同步响应
            response = await text_service.generate_text(
                request_data,
                user_id=user_id,
                request_id=getattr(request.state, "request_id", None)
            )
            return response
            
    except AIGenHubException as e:
        raise HTTPException(status_code=400, detail=e.to_dict())
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models", response_model=Dict[str, List[str]])
async def get_supported_models(
    text_service: TextGenerationService = Depends(get_text_service)
):
    """获取支持的文本生成模型列表
    
    返回按供应商分组的模型列表
    """
    try:
        models = await text_service.get_supported_models()
        return models
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats")
async def get_service_stats(
    text_service: TextGenerationService = Depends(get_text_service)
):
    """获取文本生成服务统计信息"""
    try:
        stats = await text_service.get_service_stats()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 兼容OpenAI API格式的端点
@router.post("/chat/completions", response_model=Union[TextGenerationResponse, None])
async def chat_completions(
    request_data: TextGenerationRequest,
    request: Request,
    text_service: TextGenerationService = Depends(get_text_service),
    user_id: str = Depends(get_user_id)
):
    """OpenAI兼容的聊天完成API
    
    提供与OpenAI Chat Completions API兼容的接口
    """
    return await generate_text(request_data, request, text_service, user_id)
