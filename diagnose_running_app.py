#!/usr/bin/env python3
"""
诊断正在运行的应用程序

检查当前运行的AI Gen Hub应用状态，验证路由修复是否生效
"""

import requests
import json
import os
import subprocess
import time
from pathlib import Path
from datetime import datetime

def print_section(title):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

def print_subsection(title):
    """打印子章节标题"""
    print(f"\n📋 {title}")
    print('-'*40)

def check_app_running():
    """检查应用是否正在运行"""
    print_section("检查应用运行状态")
    
    # 常见的端口列表
    ports = [8000, 8080, 3000, 5000]
    running_ports = []
    
    for port in ports:
        try:
            response = requests.get(f"http://localhost:{port}/", timeout=2)
            if response.status_code == 200:
                running_ports.append(port)
                print(f"✅ 端口 {port} 有应用运行")
                try:
                    data = response.json()
                    if 'name' in data:
                        print(f"   应用名称: {data.get('name', 'Unknown')}")
                        print(f"   版本: {data.get('version', 'Unknown')}")
                        print(f"   环境: {data.get('environment', 'Unknown')}")
                except:
                    print(f"   响应不是JSON格式")
            else:
                print(f"❌ 端口 {port} 返回状态码: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"⚪ 端口 {port} 无连接")
        except requests.exceptions.Timeout:
            print(f"⏰ 端口 {port} 连接超时")
        except Exception as e:
            print(f"❌ 端口 {port} 检查失败: {e}")
    
    return running_ports

def test_diagnostic_endpoint(port=8000):
    """测试诊断端点"""
    print_section("测试诊断端点")
    
    try:
        response = requests.get(f"http://localhost:{port}/diagnostic", timeout=5)
        print(f"诊断端点状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 诊断端点可访问")
            
            print_subsection("应用配置信息")
            app_settings = data.get('app_settings', {})
            print(f"   环境: {app_settings.get('environment', 'Unknown')}")
            print(f"   调试模式: {app_settings.get('debug', 'Unknown')}")
            print(f"   应用名称: {app_settings.get('app_name', 'Unknown')}")
            print(f"   应用版本: {app_settings.get('app_version', 'Unknown')}")
            
            print_subsection("路由信息")
            routes = data.get('routes', {})
            print(f"   总路由数量: {routes.get('total', 0)}")
            print(f"   调试路由数量: {routes.get('debug_routes', 0)}")
            
            print_subsection("调试路由注册条件")
            condition = data.get('debug_route_condition', {})
            print(f"   调试模式: {condition.get('debug_mode', 'Unknown')}")
            print(f"   环境: {condition.get('environment', 'Unknown')}")
            print(f"   应该注册调试路由: {condition.get('should_register', 'Unknown')}")
            
            if routes.get('debug_routes', 0) > 0:
                print_subsection("调试路由列表")
                debug_routes = routes.get('debug_routes_list', [])
                for route in debug_routes[:10]:  # 只显示前10个
                    print(f"   {route}")
                if len(debug_routes) > 10:
                    print(f"   ... 还有 {len(debug_routes) - 10} 个路由")
            
            return data
        else:
            print(f"❌ 诊断端点返回错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到诊断端点 - 应用可能未运行")
        return None
    except Exception as e:
        print(f"❌ 诊断端点测试失败: {e}")
        return None

def test_debug_endpoints(port=8000):
    """测试调试端点"""
    print_section("测试调试端点")
    
    test_endpoints = [
        "/debug/",
        "/debug/api/system/info",
        "/debug/system",
        "/debug/api-test"
    ]
    
    results = {}
    
    for endpoint in test_endpoints:
        try:
            response = requests.get(f"http://localhost:{port}{endpoint}", timeout=5)
            status = response.status_code
            results[endpoint] = status
            
            if status == 200:
                print(f"✅ {endpoint} - 200 OK")
            elif status == 404:
                print(f"❌ {endpoint} - 404 Not Found")
            elif status == 401:
                print(f"🔒 {endpoint} - 401 Unauthorized")
            elif status == 403:
                print(f"🔒 {endpoint} - 403 Forbidden")
            else:
                print(f"⚠️ {endpoint} - {status}")
                
        except Exception as e:
            print(f"❌ {endpoint} - 异常: {e}")
            results[endpoint] = "ERROR"
    
    return results

def check_file_modifications():
    """检查关键文件的修改时间"""
    print_section("检查文件修改时间")
    
    key_files = [
        "src/ai_gen_hub/api/app.py",
        "src/ai_gen_hub/config/settings.py", 
        "src/ai_gen_hub/api/middleware.py",
        ".env"
    ]
    
    for file_path in key_files:
        path = Path(file_path)
        if path.exists():
            mtime = path.stat().st_mtime
            mod_time = datetime.fromtimestamp(mtime)
            print(f"✅ {file_path}")
            print(f"   修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print(f"❌ {file_path} - 文件不存在")

def check_git_status():
    """检查Git状态"""
    print_section("检查Git状态")
    
    try:
        # 检查当前分支和提交
        result = subprocess.run(['git', 'log', '--oneline', '-3'], 
                              capture_output=True, text=True, cwd='.')
        if result.returncode == 0:
            print("📝 最近的提交:")
            for line in result.stdout.strip().split('\n'):
                print(f"   {line}")
        
        # 检查工作区状态
        result = subprocess.run(['git', 'status', '--porcelain'], 
                              capture_output=True, text=True, cwd='.')
        if result.returncode == 0:
            if result.stdout.strip():
                print("\n⚠️ 工作区有未提交的更改:")
                for line in result.stdout.strip().split('\n'):
                    print(f"   {line}")
            else:
                print("\n✅ 工作区干净，无未提交更改")
                
    except Exception as e:
        print(f"❌ Git状态检查失败: {e}")

def check_environment_variables():
    """检查环境变量"""
    print_section("检查环境变量")
    
    key_vars = ['DEBUG', 'ENVIRONMENT', 'APP_NAME', 'API_HOST', 'API_PORT']
    
    print("🔍 当前进程环境变量:")
    for var in key_vars:
        value = os.environ.get(var)
        if value:
            print(f"   ✅ {var} = {value}")
        else:
            print(f"   ❌ {var} = (未设置)")
    
    # 检查.env文件
    env_file = Path('.env')
    if env_file.exists():
        print(f"\n📄 .env文件内容:")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):
                    print(f"   {line_num:2d}: {line}")
    else:
        print("\n❌ .env文件不存在")

def check_process_info():
    """检查进程信息"""
    print_section("检查进程信息")
    
    try:
        # 查找Python进程
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            python_processes = [line for line in lines if 'python' in line.lower() and ('ai_gen_hub' in line or 'uvicorn' in line or 'fastapi' in line)]
            
            if python_processes:
                print("🔍 相关Python进程:")
                for proc in python_processes:
                    print(f"   {proc}")
            else:
                print("❌ 未找到相关Python进程")
                
    except Exception as e:
        print(f"❌ 进程检查失败: {e}")

def provide_recommendations(diagnostic_data, debug_results):
    """提供修复建议"""
    print_section("修复建议")
    
    if not diagnostic_data:
        print("🚨 应用未运行或诊断端点不可访问")
        print("\n📋 建议步骤:")
        print("1. 确认应用是否正在运行")
        print("2. 检查应用启动日志")
        print("3. 重新启动应用")
        print("\n🔧 重启命令:")
        print("   source venv/bin/activate")
        print("   python run_server.py")
        return
    
    app_settings = diagnostic_data.get('app_settings', {})
    routes = diagnostic_data.get('routes', {})
    condition = diagnostic_data.get('debug_route_condition', {})
    
    print("📊 诊断结果分析:")
    
    # 检查调试模式
    if not condition.get('should_register', False):
        print("❌ 调试路由注册条件不满足")
        print("   当前环境:", app_settings.get('environment'))
        print("   调试模式:", app_settings.get('debug'))
        print("\n🔧 修复方法:")
        print("   1. 设置 DEBUG=true 或 ENVIRONMENT=development")
        print("   2. 重启应用")
    elif routes.get('debug_routes', 0) == 0:
        print("❌ 调试路由应该注册但未找到")
        print("\n🔧 修复方法:")
        print("   1. 检查代码是否最新")
        print("   2. 重启应用")
        print("   3. 检查应用启动日志")
    elif any(status == 404 for status in debug_results.values()):
        print("❌ 调试路由已注册但返回404")
        print("\n🔧 可能原因:")
        print("   1. 应用使用了旧版本代码")
        print("   2. 路由注册失败")
        print("   3. 中间件配置问题")
        print("\n🔧 修复方法:")
        print("   1. 确认代码已更新并重启应用")
        print("   2. 检查应用启动日志中的路由注册信息")
    else:
        print("✅ 调试路由配置正常")

def main():
    """主诊断函数"""
    print("🚀 AI Gen Hub 运行状态诊断")
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查应用运行状态
    running_ports = check_app_running()
    
    if not running_ports:
        print("\n❌ 未检测到运行中的应用")
        print("\n🔧 请先启动应用:")
        print("   source venv/bin/activate")
        print("   python run_server.py")
        return
    
    # 使用第一个检测到的端口
    port = running_ports[0]
    print(f"\n✅ 使用端口 {port} 进行诊断")
    
    # 2. 测试诊断端点
    diagnostic_data = test_diagnostic_endpoint(port)
    
    # 3. 测试调试端点
    debug_results = test_debug_endpoints(port)
    
    # 4. 检查文件状态
    check_file_modifications()
    
    # 5. 检查Git状态
    check_git_status()
    
    # 6. 检查环境变量
    check_environment_variables()
    
    # 7. 检查进程信息
    check_process_info()
    
    # 8. 提供修复建议
    provide_recommendations(diagnostic_data, debug_results)
    
    print(f"\n{'='*60}")
    print("🎯 诊断完成")
    print('='*60)

if __name__ == "__main__":
    main()
