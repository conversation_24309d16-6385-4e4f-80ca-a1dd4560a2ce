#!/usr/bin/env python3
"""
修复并重启AI Gen Hub应用

停止旧的应用进程，启动修复后的应用，并验证修复效果
"""

import os
import signal
import subprocess
import time
import requests
import sys
from pathlib import Path

def print_section(title):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🔧 {title}")
    print('='*60)

def kill_existing_processes():
    """停止现有的应用进程"""
    print_section("停止现有应用进程")
    
    try:
        # 查找相关进程
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            
            # 查找需要停止的进程
            processes_to_kill = []
            for line in lines:
                if any(keyword in line for keyword in ['debug_standalone.py', 'start_with_debug.py', 'run_server.py']):
                    parts = line.split()
                    if len(parts) > 1:
                        try:
                            pid = int(parts[1])
                            processes_to_kill.append((pid, parts[10] if len(parts) > 10 else 'unknown'))
                        except ValueError:
                            continue
            
            if processes_to_kill:
                print("🔍 找到以下进程:")
                for pid, cmd in processes_to_kill:
                    print(f"   PID {pid}: {cmd}")
                
                print("\n🛑 停止进程...")
                for pid, cmd in processes_to_kill:
                    try:
                        os.kill(pid, signal.SIGTERM)
                        print(f"   ✅ 已发送SIGTERM到PID {pid}")
                    except ProcessLookupError:
                        print(f"   ⚪ PID {pid} 已不存在")
                    except PermissionError:
                        print(f"   ❌ 无权限停止PID {pid}")
                
                # 等待进程停止
                print("\n⏳ 等待进程停止...")
                time.sleep(3)
                
                # 强制停止仍在运行的进程
                for pid, cmd in processes_to_kill:
                    try:
                        os.kill(pid, 0)  # 检查进程是否仍存在
                        print(f"   🔨 强制停止PID {pid}")
                        os.kill(pid, signal.SIGKILL)
                    except ProcessLookupError:
                        pass  # 进程已停止
                
                print("✅ 进程停止完成")
            else:
                print("✅ 未找到需要停止的进程")
                
    except Exception as e:
        print(f"❌ 停止进程时出错: {e}")

def check_port_available(port=8000):
    """检查端口是否可用"""
    print_section(f"检查端口{port}可用性")
    
    try:
        response = requests.get(f"http://localhost:{port}/", timeout=2)
        print(f"❌ 端口{port}仍被占用")
        return False
    except requests.exceptions.ConnectionError:
        print(f"✅ 端口{port}可用")
        return True
    except Exception as e:
        print(f"⚠️ 端口检查异常: {e}")
        return True

def load_env_variables():
    """加载环境变量"""
    print_section("加载环境变量")
    
    env_file = Path('.env')
    if env_file.exists():
        print("📄 加载.env文件...")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
                    print(f"   ✅ {key.strip()} = {value.strip()}")
        print("✅ 环境变量加载完成")
    else:
        print("❌ .env文件不存在")

def start_fixed_app():
    """启动修复后的应用"""
    print_section("启动修复后的应用")
    
    # 确保在虚拟环境中
    venv_python = Path('venv/bin/python')
    if venv_python.exists():
        python_cmd = str(venv_python)
        print("✅ 使用虚拟环境Python")
    else:
        python_cmd = sys.executable
        print("⚠️ 使用系统Python")
    
    print(f"🚀 启动命令: {python_cmd} run_server.py")
    
    try:
        # 启动应用
        process = subprocess.Popen(
            [python_cmd, 'run_server.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=os.environ.copy()
        )
        
        print(f"✅ 应用已启动，PID: {process.pid}")
        
        # 等待应用启动
        print("⏳ 等待应用启动...")
        time.sleep(5)
        
        # 检查进程是否仍在运行
        if process.poll() is None:
            print("✅ 应用正在运行")
            return process
        else:
            print("❌ 应用启动失败")
            stdout, stderr = process.communicate()
            print(f"标准输出: {stdout}")
            print(f"错误输出: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 启动应用失败: {e}")
        return None

def verify_fix():
    """验证修复效果"""
    print_section("验证修复效果")
    
    max_retries = 6
    for attempt in range(max_retries):
        print(f"🔍 验证尝试 {attempt + 1}/{max_retries}")
        
        try:
            # 测试诊断端点
            response = requests.get("http://localhost:8000/diagnostic", timeout=5)
            if response.status_code == 200:
                print("✅ 诊断端点可访问")
                
                data = response.json()
                app_settings = data.get('app_settings', {})
                routes = data.get('routes', {})
                
                print(f"   环境: {app_settings.get('environment')}")
                print(f"   调试模式: {app_settings.get('debug')}")
                print(f"   调试路由数量: {routes.get('debug_routes', 0)}")
                
                if routes.get('debug_routes', 0) > 0:
                    print("✅ 调试路由已注册")
                    
                    # 测试调试端点
                    debug_response = requests.get("http://localhost:8000/debug/api/system/info", timeout=5)
                    if debug_response.status_code == 200:
                        print("✅ 调试端点可访问")
                        print("🎉 修复验证成功！")
                        return True
                    else:
                        print(f"❌ 调试端点返回: {debug_response.status_code}")
                else:
                    print("❌ 调试路由未注册")
                
            else:
                print(f"❌ 诊断端点返回: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print("⏳ 应用尚未就绪，等待...")
        except Exception as e:
            print(f"❌ 验证异常: {e}")
        
        if attempt < max_retries - 1:
            time.sleep(5)
    
    print("❌ 修复验证失败")
    return False

def show_usage_info():
    """显示使用信息"""
    print_section("使用信息")
    
    print("🎯 修复完成！现在您可以访问以下端点:")
    print()
    print("📊 诊断端点:")
    print("   http://localhost:8000/diagnostic")
    print()
    print("🔧 调试端点:")
    print("   http://localhost:8000/debug/")
    print("   http://localhost:8000/debug/api/system/info")
    print("   http://localhost:8000/debug/system")
    print("   http://localhost:8000/debug/api-test")
    print("   http://localhost:8000/debug/logs")
    print("   http://localhost:8000/debug/config")
    print("   http://localhost:8000/debug/metrics")
    print("   http://localhost:8000/debug/tools")
    print()
    print("📚 API文档:")
    print("   http://localhost:8000/docs")
    print()
    print("🔍 测试命令:")
    print("   curl http://localhost:8000/diagnostic")
    print("   curl http://localhost:8000/debug/api/system/info")

def main():
    """主函数"""
    print("🚀 AI Gen Hub 应用修复和重启工具")
    print(f"执行时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 停止现有进程
    kill_existing_processes()
    
    # 2. 检查端口可用性
    if not check_port_available():
        print("❌ 端口仍被占用，请手动检查")
        return False
    
    # 3. 加载环境变量
    load_env_variables()
    
    # 4. 启动修复后的应用
    process = start_fixed_app()
    if not process:
        print("❌ 应用启动失败")
        return False
    
    # 5. 验证修复效果
    if verify_fix():
        show_usage_info()
        print("\n✅ 修复和重启完成！")
        return True
    else:
        print("\n❌ 修复验证失败，请检查应用日志")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        sys.exit(1)
